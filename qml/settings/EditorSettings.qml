import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtCharts 2.15
import QtQuick.Layouts 1.0
import '../'

Item{
    id:root
    anchors.fill:parent

    Column{
        anchors.fill:parent
        spacing:10

        //TEXT
        Rectangle{
            height:40
            width:parent.width
            color:'transparent'
            Text{
                text:"Editor Settings"
                font.bold:true
                font.pointSize:18
                color:'#AAAAAA'
                anchors.verticalCenter: parent.verticalCenter
                anchors.left:parent.left
                anchors.margins:10
            }

            //BUTTON
            Rectangle{
                height: parent.height
                width: Math.min((parent.width*.2),130)
                color:parent.color
                anchors.right:parent.right
                anchors.margins:15
                Rectangle{
                    color:'#2E2F30'
                    radius:8
                    height: parent.height-10
                    width: parent.width-30
                    border.color:'#464647'
                    anchors.centerIn: parent
                    Text{
                        text:'save'
                        color:'#EEEEEE'
                        font.pointSize:12
                        anchors.centerIn: parent
                    }
                    MouseArea{
                        anchors.fill:parent
                        hoverEnabled: true
                        onEntered: {
                            parent.color='#464647'
                        }
                        onExited: {
                            parent.color='#2E2F30'
                        }
                        onClicked: {
                            //
                        }
                    }
                }
            }
        }

        Rectangle{
            height:parent.height-50
            width:parent.width
            color:'transparent'

            ScrollView{
                anchors.fill:parent
                clip:true

                Column{
                    spacing:10
                    anchors.fill:parent
                    anchors.leftMargin:10
                    anchors.rightMargin:30
                    // anchors.horizontalCenter: parent.horizontalCenter

                    //TEXT
                    Rectangle{
                        height:20
                        width:parent.width
                        color:'transparent'
                        Text{
                            text:"FONT SIZE"
                            // font.bold:true
                            font.pointSize:12
                            color:'#AAAAAA'
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left:parent.left
                            anchors.margins:10
                        }
                    }

                    //FONT SIZE
                    Rectangle{
                        height: 50
                        width:parent.parent.width
                        color:'transparent'

                        Input{
                            id:fontsizeinput
                            h:parent.height
                            w:parent.width*.70
                            rad:0
                            backcolor:'#292828'
                            plhc:'#CCCCCC'
                            plhtext:'12'
                            valid:RegExpValidator{
                                regExp: /^([1-9]+)$/
                            }
                        }
                    }

                    //TEXT
                    Rectangle{
                        height:20
                        width:parent.width
                        color:'transparent'
                        Text{
                            text:"FONT FAMILY"
                            // font.bold:true
                            font.pointSize:12
                            color:'#AAAAAA'
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left:parent.left
                            anchors.margins:10
                        }
                    }

                    //FONT FAMILY
                    Rectangle{
                        height:50
                        width:parent.parent.width
                        color:'transparent'

                        Input{
                            id:fontfamilyinput
                            h:parent.height
                            w:parent.width*.70
                            rad:0
                            backcolor:'#292828'
                            plhc:'#CCCCCC'
                            plhtext:'monospace'
                            valid:RegExpValidator{
                                regExp: /^([a-zA-Z1-9._\-]+)$/
                            }
                        }
                    }

                    //TEXT
                    Rectangle{
                        height:20
                        width:parent.width
                        color:'transparent'
                        Text{
                            text:"TAB SIZE"
                            // font.bold:true
                            font.pointSize:12
                            color:'#AAAAAA'
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left:parent.left
                            anchors.margins:10
                        }
                    }

                    //TAB SIZE
                    Rectangle{
                        height: 50
                        width:parent.parent.width
                        color:'transparent'

                        Input{
                            id:tabsizeinput
                            h:parent.height
                            w:parent.width*.70
                            rad:0
                            backcolor:'#292828'
                            plhc:'#CCCCCC'
                            plhtext:'4'
                            valid:RegExpValidator{
                                regExp: /^([1-9]+)$/
                            }
                        }
                    }

                    //TEXT
                    Rectangle{
                        height:20
                        width:parent.width
                        color:'transparent'
                        Text{
                            text:"WORD WRAP (YES/NO)"
                            // font.bold:true
                            font.pointSize:12
                            color:'#AAAAAA'
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left:parent.left
                            anchors.margins:10
                        }
                    }

                    //WORD WRAP TRUE/FALSE
                    Rectangle{
                        height:50
                        width:parent.width
                        color:'transparent'

                        CustomCheckWithHelper{
                            id:wordwrapcheck
                            anchors.fill:parent
                            helper:"Controls if the lines should wrap in the code editor"
                        }
                    }

                    //TEXT
                    Rectangle{
                        height:20
                        width:parent.width
                        color:'transparent'
                        Text{
                            text:"AUTO CLOSING BRACKETS"
                            // font.bold:true
                            font.pointSize:12
                            color:'#AAAAAA'
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left:parent.left
                            anchors.margins:10
                        }
                    }

                    //AUTO CLOSING BRACKETS
                    Rectangle{
                        height: 50
                        width:parent.width
                        color:'transparent'

                        CustomCheckWithHelper{
                            id:closebracketscheck
                            anchors.fill:parent
                            helper:"This will close automaticaly brackets when editing text"
                        }
                    }

                    //TEXT
                    Rectangle{
                        height:20
                        width:parent.width
                        color:'transparent'
                        Text{
                            text:"AUTO CLOSING QUOTES"
                            // font.bold:true
                            font.pointSize:12
                            color:'#AAAAAA'
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left:parent.left
                            anchors.margins:10
                        }
                    }

                    //AUTO CLOSING QUOTES 
                    Rectangle{
                        height:50
                        width:parent.width
                        color:'transparent'

                        CustomCheckWithHelper{
                            id:closequotescheck
                            anchors.fill:parent
                            helper:"This will automaticaly close quotes when editing text"
                        }
                    }

                    //TEXT
                    Rectangle{
                        height:20
                        width:parent.width
                        color:'transparent'
                        Text{
                            text:"MINIMAP (YES/NO)"
                            // font.bold:true
                            font.pointSize:12
                            color:'#AAAAAA'
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left:parent.left
                            anchors.margins:10
                        }
                    }

                    //MINIMAP TRUE/FALSE
                    Rectangle{
                        height: 50
                        width:parent.width
                        color:'transparent'

                        CustomCheckWithHelper{
                            id:minimapcheck
                            anchors.fill:parent
                            helper:"Controls if the minimap should be visible"
                        }
                    }
                }
            }
        }
    }
}