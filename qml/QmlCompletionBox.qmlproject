/* File generated by Qt Creator, version 2.6.2 */

import QmlProject 2.15

Project {
    mainFile: "Main.qml"

    /* Include .qml, .js, and image files from current directory and subdirectories */
    QmlFiles {
        directory: "."
    }
    JavaScriptFiles {
        directory: "."
    }
    ImageFiles {
        directory: "."
    }
    /* List of plugin directories passed to QML runtime */
    // importPaths: [ "../exampleplugin" ]
}
