{"backoff": 0, "has_more": false, "page": 4, "quota_max": 300, "quota_remaining": 265, "total": 0, "items": [{"tags": ["python", "postgresql", "postgis", "qgis", "pyqgis"], "owner": {"reputation": 1419, "user_id": 261114, "user_type": "registered", "accept_rate": 78, "profile_image": "https://www.gravatar.com/avatar/e96bef3acd2c32a183bb8d031f36ec96?s=256&d=identicon&r=PG", "display_name": "Quickredfox", "link": "https://stackoverflow.com/users/261114/quickredfox"}, "is_answered": false, "view_count": 17, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 76343280, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/76343280/unable-to-find-postgis-layer-using-pyqgis-in-a-standalone-script", "title": "Unable to find PostGIS layer using PyQGIS in a standalone script"}, {"tags": ["python", "postgresql", "windows", "postgresql-13", "plpython"], "owner": {"reputation": 6669, "user_id": 11154841, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/a3cGr.png?s=256&g=1", "display_name": "questionto42", "link": "https://stackoverflow.com/users/11154841/questionto42"}, "is_answered": true, "view_count": 5815, "accepted_answer_id": 69794464, "answer_count": 4, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 67852675, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67852675/postgresql-13-python-3-7-9-plpython3u-psql-server-closed-the-connection-u", "title": "PostgreSQL 13 + Python 3.7.9 + plpython3u: &#39;psql: server closed the connection unexepectedly.&#39; + &#39;The application has lost the database connection.&#39;"}, {"tags": ["python", "sqlite", "flask", "apache2"], "owner": {"reputation": 23, "user_id": 21932305, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AOh14GjV3LsiDFiy-Z_spkkhavUiEtLLgMBc_fQaVq1z=k-s256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/21932305/lacey"}, "is_answered": false, "view_count": 37, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 76300935, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/76300935/unable-to-open-database-file-for-sqlite3-in-my-flask-app", "title": "unable to open database file for sqlite3 in my Flask app"}, {"tags": ["python", "sqlite", "database-connection", "operationalerror"], "owner": {"reputation": 21, "user_id": 1947597, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/5a1b4acee18aa8cf633ffff5f3fe043a?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/1947597/chintan-upadhyay"}, "is_answered": false, "view_count": 5139, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 55156557, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/55156557/sqlite3-connect-not-working-with-relative-path", "title": "sqlite3.connect not working with relative path"}, {"tags": ["python", "azure", "sqlite", "azure-web-app-service"], "owner": {"reputation": 393, "user_id": 21362272, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/fdc762028f2eb91951d244a4fb38e7d1?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/21362272/ajay-managaon"}, "is_answered": false, "view_count": 66, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 76277858, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/76277858/unable-to-open-database-file-on-azure-app-service", "title": "unable to open database file on Azure App Service"}, {"tags": ["python", "django", "apache", "sqlite", "centos"], "owner": {"reputation": 224, "user_id": 5033855, "user_type": "registered", "accept_rate": 80, "profile_image": "https://i.stack.imgur.com/3HQpw.jpg?s=256&g=1", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/5033855/flowneee"}, "is_answered": true, "view_count": 10466, "accepted_answer_id": 36797954, "answer_count": 4, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 36797051, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/36797051/django-login-error-attempt-to-write-a-readonly-database", "title": "Django login error: &quot;attempt to write a readonly database&quot;"}, {"tags": ["python", "azure", "sqlite", "azure-web-app-service", "azure-files"], "owner": {"reputation": 45, "user_id": 13427691, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1414cc1c15823c4f9824bb941a183cc4?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "anon", "link": "https://stackoverflow.com/users/13427691/anon"}, "is_answered": true, "view_count": 131, "accepted_answer_id": 76167664, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 76166628, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/76166628/can-i-store-and-then-read-write-to-a-sqlite-database-using-azure-file-storage-fr", "title": "Can I store and then read/write to a SQLite database using Azure File storage from an App Service?"}, {"tags": ["python", "tkinter", "tkinter-button", "ttkbootstrap"], "owner": {"reputation": 111, "user_id": 8850685, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/d41118d28495ca0c7415c40b809a1e6e?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user350331", "link": "https://stackoverflow.com/users/8850685/user350331"}, "is_answered": false, "view_count": 57, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 76173653, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/76173653/consecutive-mainloop-calls-malfunctioning-in-ttkbootstrap", "title": "Consecutive mainloop calls malfunctioning in ttkbootstrap"}, {"tags": ["python", "sql", "postgresql", "queue", "message-queue"], "owner": {"reputation": 3607, "user_id": 11693768, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/535575da7ebf9dcc3dac6b745684a79e?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "anarchy", "link": "https://stackoverflow.com/users/11693768/anarchy"}, "is_answered": true, "view_count": 161, "accepted_answer_id": 76002874, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 73958147, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/73958147/how-to-use-sql-table-row-items-or-other-alternatives-as-a-queue-for-multiple-s", "title": "How to use sql table row items (or other alternatives) as a queue for multiple servers"}, {"tags": ["python", "keyerror"], "owner": {"reputation": 1, "user_id": 21549542, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/8535110f91f74447bce92f41489b7478?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user21549542", "link": "https://stackoverflow.com/users/21549542/user21549542"}, "is_answered": false, "view_count": 44, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 75933848, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/75933848/sql-alchemy-keyerror", "title": "SQL Alchemy KeyError"}, {"tags": ["python", "sqlite", "flask", "sqlalchemy", "centos8"], "owner": {"reputation": 21, "user_id": 13855927, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/04ee602c3da1df98bca4172df9074491?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "kbizzle", "link": "https://stackoverflow.com/users/13855927/kbizzle"}, "is_answered": true, "view_count": 4170, "answer_count": 3, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 62705340, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/62705340/sqlalchemy-db-create-all-error-not-creating-db", "title": "SQLAlchemy db.create_all() Error, not creating db"}, {"tags": ["python", "csv", "sqlalchemy"], "owner": {"reputation": 15, "user_id": 16693369, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/G0HZ2.jpg?s=256&g=1", "display_name": "Born2Student", "link": "https://stackoverflow.com/users/16693369/born2student"}, "is_answered": false, "view_count": 383, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 73532350, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/73532350/i-get-this-python-csv-typeerror-is-an-invalid-keyword-argument-for-tasks", "title": "I Get This Python CSV TypeError: &#39;&#39; is an invalid keyword argument for Tasks"}, {"tags": ["python", "sqlite", "data-science"], "owner": {"reputation": 51, "user_id": 17665172, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/b2b1ae23a13245c5d28e8742de818383?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/17665172/leo-hanhart"}, "is_answered": false, "view_count": 69, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 72141358, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/72141358/python-cant-find-database-if-script-is-run-from-other-file", "title": "Python cant find database if script is run from other file"}, {"tags": ["python", "sqlalchemy", "python-sphinx"], "owner": {"reputation": 11, "user_id": 14847203, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/536d82f81de7d12a96a60205eec15863?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/14847203/sebastien-fauque"}, "is_answered": false, "view_count": 27, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 75689652, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/75689652/sphinx-documentation-build-produces-an-error-sqlite3-operationalerror-unable-t", "title": "Sphinx documentation build produces an error: sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "sqlite", "deployment", "flask-sqlalchemy"], "owner": {"reputation": 11, "user_id": 7866983, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/72cf0d2dda9f9d01a4b23c505a41274b?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "krsmith28p", "link": "https://stackoverflow.com/users/7866983/krsmith28p"}, "is_answered": true, "view_count": 541, "answer_count": 1, "score": -2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 75277994, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/75277994/how-do-i-control-where-sqlalchemy-places-a-sqlite-db-file", "title": "How do I control where SQLAlchemy places a sqlite db file"}, {"tags": ["python", "azure", "odbc", "azure-sql-database"], "owner": {"reputation": 688, "user_id": 5368122, "user_type": "registered", "accept_rate": 20, "profile_image": "https://i.stack.imgur.com/vHUTC.jpg?s=256&g=1", "display_name": "Obiii", "link": "https://stackoverflow.com/users/5368122/obiii"}, "is_answered": true, "view_count": 424, "accepted_answer_id": 75195876, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 75184483, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/75184483/connect-to-azure-sql-with-managed-identity-python", "title": "Connect to azure sql with managed identity python"}, {"tags": ["python", "sqlite", "systemd", "os.path"], "owner": {"reputation": 11, "user_id": 9733822, "user_type": "registered", "profile_image": "https://lh5.googleusercontent.com/-k_ypKUO-DzY/AAAAAAAAAAI/AAAAAAAACqw/WZ3sXEyeI-Y/photo.jpg?sz=256", "display_name": "<PERSON><PERSON>umar", "link": "https://stackoverflow.com/users/9733822/sarath-kumar"}, "is_answered": false, "view_count": 97, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 75217744, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/75217744/how-to-set-os-path-correctly-shows-a-different-path-when-ran-by-systemd-service", "title": "How to set os.path correctly. Shows a different path when ran by systemd service"}, {"tags": ["python", "visual-studio-code"], "owner": {"reputation": 6125, "user_id": 3788557, "user_type": "registered", "accept_rate": 46, "profile_image": "https://www.gravatar.com/avatar/d5bba1829d9153d8cae1fc8ccdddcd11?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "runningbirds", "link": "https://stackoverflow.com/users/3788557/runningbirds"}, "is_answered": false, "view_count": 52, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 75087444, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/75087444/unable-to-import-python-package-in-vs-code-but-no-issues-in-terminal", "title": "Unable to import python package in vs-code but, no issues in terminal"}, {"tags": ["python", "sqlite", "sqlalchemy", "dask", "dask-dataframe"], "owner": {"reputation": 5519, "user_id": 702846, "user_type": "registered", "accept_rate": 49, "profile_image": "https://www.gravatar.com/avatar/ad589b0712b73d8b534d9a7bf5e2f40d?s=256&d=identicon&r=PG", "display_name": "Are<PERSON>", "link": "https://stackoverflow.com/users/702846/areza"}, "is_answered": true, "view_count": 81, "accepted_answer_id": 75086108, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 75084453, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/75084453/populate-sql-database-with-dask-dataframe-and-dump-into-a-file", "title": "populate SQL database with dask dataframe and dump into a file"}, {"tags": ["python", "microservices", "flask-sqlalchemy"], "owner": {"reputation": 1, "user_id": 20685730, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/089442321c26a940cfc3442978eb4a52?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/20685730/muham<PERSON>-r<PERSON><PERSON><PERSON>-alam"}, "is_answered": false, "view_count": 359, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 74681171, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74681171/flask-db-migrate-not-working-sqlalchemy-exc-operationalerror-sqlite3-operation", "title": "flask db migrate not working sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) unable to open database file (Background on this error at:"}, {"tags": ["python", "python-3.x", "sqlite", "chatterbot"], "owner": {"reputation": 1, "user_id": 15023599, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AOh14Giipkg0FN5vyPfGYX_S5Y5dleIOGfAwma8uqxto=k-s256", "display_name": "Lasauce6", "link": "https://stackoverflow.com/users/15023599/lasauce6"}, "is_answered": false, "view_count": 226, "answer_count": 1, "score": -2, "last_activity_date": **********, "creation_date": **********, "question_id": 65760197, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/65760197/problem-with-sqlite3-operational-error-in-chatterbot", "title": "Problem with sqlite3.Operational error in chatterbot"}, {"tags": ["python", "postgresql", "pyspark", "docker-compose", "debian"], "owner": {"reputation": 15, "user_id": 19846154, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/a90cbcd0a2de65a106a46d0cc72a4aac?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Data writer", "link": "https://stackoverflow.com/users/19846154/data-writer"}, "is_answered": false, "view_count": 134, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 74593063, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74593063/getting-error-in-docker-compose-up-build", "title": "Getting error in docker-compose up --build"}, {"tags": ["python", "sqlite", "sqlalchemy", "flask-sqlalchemy"], "owner": {"reputation": 11, "user_id": 7554588, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/ae9e0d740e21326650238260ea470c39?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/7554588/liam"}, "is_answered": false, "view_count": 72, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 74511644, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74511644/unable-to-open-database-file-error-on-connect", "title": "&quot;unable to open database file&quot; error on connect()"}, {"tags": ["python", "parsing", "exception", "command-line"], "owner": {"reputation": 1, "user_id": 20344949, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/7c55601330214d51c2f1bdf1cc75a6f9?s=256&d=identicon&r=PG", "display_name": "ir44", "link": "https://stackoverflow.com/users/20344949/ir44"}, "is_answered": false, "view_count": 21, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 74470720, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74470720/how-to-run-an-sql-file-full-of-commands-as-a-command-line-argument-to-a-python", "title": "How to run an .sql file full of commands as a command line argument to a python program?"}, {"tags": ["python", "sqlalchemy"], "owner": {"reputation": 1, "user_id": 20367782, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/e04918506a7606e7e5aae9a180b3db75?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/20367782/hidan"}, "is_answered": false, "view_count": 59, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 74424499, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74424499/cant-open-data-base-sqlalchemy", "title": "Cant open data base sqlalchemy"}, {"tags": ["python", "sqlite", "flask", "flask-sqlalchemy"], "owner": {"reputation": 33, "user_id": 10338858, "user_type": "registered", "profile_image": "https://lh6.googleusercontent.com/-meSpR7c4muE/AAAAAAAAAAI/AAAAAAAAAAA/APUIFaO2onlNt79cK0hKff4_vSr6UR6R_g/mo/photo.jpg?sz=256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/10338858/steve-piller"}, "is_answered": false, "view_count": 437, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 74352935, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74352935/cant-connect-to-database-after-python-and-sqlalchemy-updates", "title": "Can&#39;t connect to database after Python and SQLAlchemy updates"}, {"tags": ["python", "flask-sqlalchemy", "operationalerror"], "owner": {"reputation": 1, "user_id": 18092739, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a/AATXAJy1EV1h8b2duRImLHCs4f8sMNuoQDND-JbbCogV=k-s256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/18092739/conor-segreti"}, "is_answered": false, "view_count": 28, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 74302915, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74302915/issue-deploying-python-flask-package-more-specifically-unable-to-open-database", "title": "Issue deploying Python Flask Package, more specifically unable to open database file"}, {"tags": ["python", "amazon-web-services", "sqlite", "amazon-ec2", "pyrogram"], "owner": {"reputation": 98, "user_id": 19670508, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AFdZucrtFRsace6J9EsVwTRzqvev4Rnkx0tPbco_hGpR=k-s256", "display_name": "sleepypanda", "link": "https://stackoverflow.com/users/19670508/sleepypanda"}, "is_answered": true, "view_count": 165, "accepted_answer_id": 74231116, "answer_count": 1, "score": -2, "last_activity_date": **********, "creation_date": **********, "question_id": 74230467, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74230467/how-to-fix-the-issue-of-sqlite3-operationalerror-unable-to-open-database-file", "title": "How to fix the issue of {sqlite3.OperationalError: unable to open database file} in aws-ec2 instance ubuntu machine?"}, {"tags": ["python", "sqlite", "pip"], "owner": {"reputation": 105, "user_id": 5436777, "user_type": "registered", "accept_rate": 21, "profile_image": "https://i.stack.imgur.com/p9B8z.png?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/5436777/r-jackson"}, "is_answered": false, "view_count": 22, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 74142618, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74142618/cannot-open-sqlite-db-file-in-python-package-distribution", "title": "Cannot open SQLite db file in python package distribution"}, {"tags": ["python", "python-3.x", "pandas", "parsing", "read.csv"], "owner": {"reputation": 31, "user_id": 15355592, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/-oMRVvlAFf1s/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuck1di0xG2ZKBFFzGJxEJvQoHVwzww/s96-c/photo.jpg?sz=256", "display_name": "S HUMA SHAH", "link": "https://stackoverflow.com/users/15355592/s-huma-shah"}, "is_answered": true, "view_count": 9210, "accepted_answer_id": 66616779, "answer_count": 3, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 66534834, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/66534834/cant-read-csv-file-emptydataerror-no-columns-to-parse-from-file", "title": "Cant read .csv file. EmptyDataError: No columns to parse from file"}, {"tags": ["python", "tkinter"], "owner": {"reputation": 393, "user_id": 8424257, "user_type": "registered", "accept_rate": 62, "profile_image": "https://www.gravatar.com/avatar/264bf6b8dee1c4ba064c75dd4cd382d7?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "nTIAO", "link": "https://stackoverflow.com/users/8424257/ntiao"}, "is_answered": false, "view_count": 11, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 74024839, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/74024839/unable-to-create-elements-within-notebook-tab-python-tkinter", "title": "Unable to create elements within notebook tab python tkinter"}, {"tags": ["python", "sqlite", "python-os"], "owner": {"reputation": 3, "user_id": 20097835, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/78e247a042b8123d6e57b2dabcda4b73?s=256&d=identicon&r=PG", "display_name": "alex_kol", "link": "https://stackoverflow.com/users/20097835/alex-kol"}, "is_answered": true, "view_count": 63, "accepted_answer_id": 73864161, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 73864102, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/73864102/not-possible-to-access-sqlite-database-a-second-time-when-using-os-chdir", "title": "Not possible to access SQLite database a second time when using os.chdir"}, {"tags": ["python", "database", "sqlite", "telegram-bot"], "owner": {"reputation": 11, "user_id": 18375732, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AOh14GhPLP_pzcCsPKFnMGBsOf6AAsy9l7AE0D1id3XNZTE=k-s256", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/18375732/anshul-gada"}, "is_answered": false, "view_count": 304, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 73778192, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/73778192/having-sqlite3-operationalerror-unable-to-open-database-file", "title": "Having sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "database", "flask"], "owner": {"reputation": 359, "user_id": 2677756, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/c4341c6179b80837c31444668ad1b063?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user2677756", "link": "https://stackoverflow.com/users/2677756/user2677756"}, "is_answered": true, "view_count": 95091, "answer_count": 12, "score": 26, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 18208492, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/18208492/sqlalchemy-exc-operationalerror-operationalerror-unable-to-open-database-file", "title": "sqlalchemy.exc.OperationalError: (OperationalError) unable to open database file None None"}, {"tags": ["python", "function", "sqlite", "tkinter"], "owner": {"reputation": 1287, "user_id": 649114, "user_type": "registered", "accept_rate": 97, "profile_image": "https://www.gravatar.com/avatar/61d64446ceafab7c220f2308333fd01f?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "daikini", "link": "https://stackoverflow.com/users/649114/daikini"}, "is_answered": true, "view_count": 131, "accepted_answer_id": 8673518, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 8673491, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/8673491/how-to-put-string-from-function-as-an-argument-for-sqlite3-connect-in-python", "title": "How to put string from function as an argument for sqlite3.connect() in python"}, {"tags": ["python", "sqlite", "flask", "sqlalchemy", "flask-sqlalchemy"], "owner": {"reputation": 116, "user_id": 11473453, "user_type": "registered", "profile_image": "https://lh4.googleusercontent.com/-MF_3QTKUuXM/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rfEty2TOijmp7kElgGvk81RJ_mRIQ/mo/photo.jpg?sz=256", "display_name": "ChungaBunga", "link": "https://stackoverflow.com/users/11473453/chungabunga"}, "is_answered": true, "view_count": 551, "accepted_answer_id": 73027125, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 72987817, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/72987817/sqlalchemy-not-connecting-to-database-file-on-networked-file-server-sqlite3-is", "title": "sqlalchemy not connecting to database file on networked file server. sqlite3 is connecting though"}, {"tags": ["python", "docker", "docker-compose", "amazon-dynamodb"], "owner": {"reputation": 135, "user_id": 13075552, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/d44a6ea8f633acc63799488bf2005580?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/13075552/springsomfan"}, "is_answered": true, "view_count": 2799, "answer_count": 1, "score": 13, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 67533058, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67533058/persist-local-dynamodb-data-in-volumes-lack-permission-unable-to-open-databa", "title": "Persist (local) dynamoDB data in volumes lack permission - unable to open database file"}, {"tags": ["python", "qt", "pyqt", "media", "qtwebengine"], "owner": {"reputation": 67, "user_id": 14932969, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/XIrK0.jpg?s=256&g=1", "display_name": "3V&#39;s", "link": "https://stackoverflow.com/users/14932969/3vs"}, "is_answered": false, "view_count": 408, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 72657441, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/72657441/qtwebengineview-crashes-python-when-loading-content", "title": "QtWebEngineView crashes python when loading content"}, {"tags": ["python", "sqlite", "telegram", "telegram-bot", "pyrogram"], "owner": {"reputation": 11, "user_id": 13065570, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AOh14GjxAmhPxO5AXWpTBu8XrZrTLh03mS1OyvQ_dPhiNA=k-s256", "display_name": "artirchique", "link": "https://stackoverflow.com/users/13065570/artirchique"}, "is_answered": false, "view_count": 557, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 72603614, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/72603614/pyrogram-client-starts-with-error-sqlite", "title": "Pyrogram client starts with error (sqlite)"}, {"tags": ["python", "hive", "sasl", "pyhive"], "owner": {"reputation": 114, "user_id": 147418, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/68fab9bb5ff070ea32ad9299d71d4a17?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/147418/dan"}, "is_answered": true, "view_count": 1406, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 69487538, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/69487538/how-to-connect-to-hive-via-pyhive-from-windows", "title": "How to Connect to Hive via pyhive from Windows"}, {"tags": ["python", "pandas", "pyodbc", "firebird-3.0"], "owner": {"reputation": 1, "user_id": 19121903, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AOh14Gg9d5s87ZoKOUGAjJGkuItEOEkB8esQlg2Qnlo3HA=k-s256", "display_name": "Volker Aizp&#250;n", "link": "https://stackoverflow.com/users/19121903/volker-aizp%c3%ban"}, "is_answered": false, "view_count": 1033, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 72250138, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/72250138/try-to-connect-a-firebird-3-05-database-to-python-pandas", "title": "Try to connect a Firebird 3.05 Database to Python (pandas)"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 84, "user_id": 15473634, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/adb77332eeb6438c15d90e68902da4fa?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user15473634", "link": "https://stackoverflow.com/users/15473634/user15473634"}, "is_answered": true, "view_count": 470, "accepted_answer_id": 72337327, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 72336984, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/72336984/python-sqlite3-operationalerror-unable-to-open-database-file", "title": "Python sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "sqlite", "sqlalchemy"], "owner": {"reputation": 101, "user_id": 1742141, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/d5fa46c32e8b167f0cf00922daa7bce0?s=256&d=identicon&r=PG", "display_name": "user1742141", "link": "https://stackoverflow.com/users/1742141/user1742141"}, "is_answered": true, "view_count": 21684, "answer_count": 2, "score": 8, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 13018298, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/13018298/cannot-connect-to-in-memory-sqlite-db-using-sqlalchemy-with-python-2-7-3-on-wind", "title": "cannot connect to in-memory SQLite DB using SQLAlchemy with Python 2.7.3 on Windows"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 5845, "user_id": 186608, "user_type": "registered", "accept_rate": 92, "profile_image": "https://www.gravatar.com/avatar/815041fede45d830060f5b3009ce7d70?s=256&d=identicon&r=PG", "display_name": "Narcolapser", "link": "https://stackoverflow.com/users/186608/narcolapser"}, "is_answered": true, "view_count": 258882, "accepted_answer_id": 4637055, "answer_count": 18, "score": 96, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 4636970, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/4636970/sqlite3-operationalerror-unable-to-open-database-file", "title": "Sqlite3, OperationalError: unable to open database file"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 55, "user_id": 6091125, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/CL2MS.jpg?s=256&g=1", "display_name": "astro", "link": "https://stackoverflow.com/users/6091125/astro"}, "is_answered": true, "view_count": 2283, "answer_count": 3, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 36121064, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/36121064/python-sqlite3-connection-path-with-spaces", "title": "python sqlite3 connection path with spaces"}, {"tags": ["python", "html", "sqlite", "pyscript"], "owner": {"reputation": 21, "user_id": 19092758, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1ba295c8a61d71406f5ce13804d2fdc6?s=256&d=identicon&r=PG", "display_name": "Sokrates19", "link": "https://stackoverflow.com/users/19092758/sokrates19"}, "is_answered": true, "view_count": 795, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 72199430, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/72199430/failed-to-open-database-file-pyscript-sqlite3", "title": "failed to open database file (Pyscript sqlite3)"}, {"tags": ["python", "github", "google-colaboratory"], "owner": {"reputation": 514, "user_id": 11713857, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/d2ab1eccb886a6b95f98bef412baa7b8?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "dereks", "link": "https://stackoverflow.com/users/11713857/dereks"}, "is_answered": true, "view_count": 1808, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 58235669, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/58235669/how-to-upload-large-files-from-colab-to-github", "title": "How to upload large files from Colab to GitHub?"}, {"tags": ["python", "mysql"], "owner": {"reputation": 1381, "user_id": 18020941, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/d8cf49b104148eaf073b8bd18b318116?s=256&d=identicon&r=PG", "display_name": "nigel239", "link": "https://stackoverflow.com/users/18020941/nigel239"}, "is_answered": false, "view_count": 172, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 71705709, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/71705709/update-images-in-sql-from-python", "title": "Update images in SQL from Python"}, {"tags": ["python", "macos", "python-2.7", "odbc", "pyodbc"], "owner": {"reputation": 4470, "user_id": 1489639, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/4fa4ec4ffb6e786c3e4370a399f9bbbf?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/1489639/turix"}, "is_answered": true, "view_count": 11291, "accepted_answer_id": 34831084, "answer_count": 3, "score": 9, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 34804404, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/34804404/cannot-import-pyodbc-on-mac", "title": "Cannot import pyodbc on Mac"}, {"tags": ["python", "dockerfile", "code-coverage", "alpine-linux"], "owner": {"reputation": 97, "user_id": 11330677, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/5mSty.jpg?s=256&g=1", "display_name": "Lomank", "link": "https://stackoverflow.com/users/11330677/lomank"}, "is_answered": true, "view_count": 2116, "accepted_answer_id": 71615741, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 71613837, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/71613837/couldnt-use-data-file-coverage-unable-to-open-database-file", "title": "Couldn&#39;t use data file .coverage: unable to open database file"}, {"tags": ["python", "sqlite", "visual-studio-code"], "owner": {"reputation": 180, "user_id": 428007, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/01d89cbe17a53cfa87629a407e23b8c3?s=256&d=identicon&r=PG", "display_name": "rocketman", "link": "https://stackoverflow.com/users/428007/rocketman"}, "is_answered": false, "view_count": 239, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 71115621, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/71115621/accessing-sqlite3-database-through-jupyter-notebook-in-vscode", "title": "accessing sqlite3 database through Jupy<PERSON> notebook in VSCODE"}, {"tags": ["python", "flask"], "owner": {"reputation": 7, "user_id": 18078839, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/2d1dcf5971c687d2acea7f9a0c8ae465?s=256&d=identicon&r=PG", "display_name": "jon doe", "link": "https://stackoverflow.com/users/18078839/jon-doe"}, "is_answered": false, "view_count": 66, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 71263710, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/71263710/python-flask-sqlite3-operationalerror", "title": "Python flask sqlite3.OperationalError"}, {"tags": ["python", "sqlite", "flask", "sqlalchemy", "pycharm"], "owner": {"reputation": 385, "user_id": 4426041, "user_type": "registered", "accept_rate": 78, "profile_image": "https://graph.facebook.com/**********/picture?type=large", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/4426041/finn-luca-frotscher"}, "is_answered": true, "view_count": 3358, "accepted_answer_id": 30419896, "answer_count": 3, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 30353714, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/30353714/python-flask-app-runs-from-wrong-folder-after-run-by-pycharm-on-127-0-0-1", "title": "Python + Flask App runs from wrong folder after run by P<PERSON><PERSON> on 127.0.0.1"}, {"tags": ["python", "ubuntu", "pya<PERSON>o", "alsa"], "owner": {"reputation": 1981, "user_id": 726730, "user_type": "registered", "accept_rate": 76, "profile_image": "https://www.gravatar.com/avatar/23e64383eeed71951c14be6abcf611a2?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/726730/chris-p"}, "is_answered": false, "view_count": 277, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 70916681, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/70916681/python-pyaudio-fix-warnings", "title": "Python PyAudio fix warnings"}, {"tags": ["python", "django", "django-rest-framework", "psycopg2", "core-api"], "owner": {"reputation": 964, "user_id": 1001286, "user_type": "registered", "accept_rate": 67, "profile_image": "https://i.stack.imgur.com/GtSyu.png?s=256&g=1", "display_name": "Pitt", "link": "https://stackoverflow.com/users/1001286/pitt"}, "is_answered": true, "view_count": 994, "answer_count": 2, "score": 13, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 49820088, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/49820088/drf-coreapiclient-psycopg2-throws-exception-interface-error-connection-alr", "title": "DRF + CoreAPIClient + psycopg2 throws exception, Interface error: connection already closed"}, {"tags": ["python", "selenium", "selenium-chromedriver"], "owner": {"reputation": 345, "user_id": 10787041, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/03d392948ab2644c1158989282b8c095?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/10787041/lloyd-thomas"}, "is_answered": true, "view_count": 435, "accepted_answer_id": 70511314, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 70511170, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/70511170/why-am-i-receiving-an-invalid-url-error-when-using-python-flask-chromedriver-an", "title": "Why am I receiving an Invalid URL Error, when using Python Flask ChromeDriver and Chrome"}, {"tags": ["python", "flask", "flask-sqlalchemy"], "owner": {"reputation": 1329, "user_id": 7522285, "user_type": "registered", "accept_rate": 78, "profile_image": "https://lh3.googleusercontent.com/-oWDhzrfxCIU/AAAAAAAAAAI/AAAAAAAAAYQ/LGuckipQR3o/photo.jpg?sz=256", "display_name": "TimothyAURA", "link": "https://stackoverflow.com/users/7522285/timothyaura"}, "is_answered": false, "view_count": 644, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 70432703, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/70432703/flask-sqlalchemy-unable-to-open-database-file", "title": "Flask SQLAlchemy - Unable to open database file"}, {"tags": ["python", "<PERSON><PERSON><PERSON>"], "owner": {"reputation": 11, "user_id": 17653904, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a/AATXAJwm4x8WZY6JnhRzl2q8fAZCQVMwCdS_VfN83TTm=k-s256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/17653904/harrison-buck"}, "is_answered": false, "view_count": 35, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 70318309, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/70318309/i-need-to-edit-overwrite-a-text-file-that-holds-a-customers-information-using-ea", "title": "I need to edit/overwrite a text file that holds a customers information using easygui and i need to select which customers information i am editing"}, {"tags": ["python", "docker", "gitlab", "devops", "docker-registry"], "owner": {"reputation": 21, "user_id": 2569854, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/36b50a163232f72f8ec9c426d8e1a14b?s=256&d=identicon&r=PG", "display_name": "user2569854", "link": "https://stackoverflow.com/users/2569854/user2569854"}, "is_answered": false, "view_count": 134, "answer_count": 0, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 70004280, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/70004280/error-when-trying-to-run-docker-container-from-gitlab-repository", "title": "error when trying to run docker container from gitlab repository"}, {"tags": ["python", "sqlite", "sqlalchemy"], "owner": {"reputation": 3457, "user_id": 4044400, "user_type": "registered", "accept_rate": 65, "profile_image": "https://www.gravatar.com/avatar/66e0ff17a11f74087ee15055ce6ab367?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/4044400/jonathan-bechtel"}, "is_answered": false, "view_count": 759, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 69959114, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/69959114/sqlite3-operationalerror-unable-to-open-database-file-when-using-absolute-file", "title": "(sqlite3.OperationalError) unable to open database file when using absolute file path in sqlite3 connection string in sqlalchemy on windows"}, {"tags": ["python", "function", "csv", "module"], "owner": {"reputation": 11, "user_id": 17177610, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/b353c0e56c1300f38e7052da81804dcd?s=256&d=identicon&r=PG", "display_name": "tim", "link": "https://stackoverflow.com/users/17177610/tim"}, "is_answered": false, "view_count": 32, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 69676529, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/69676529/error-reading-csv-file-using-function-parameters-in-two-modules-in-python", "title": "Error reading csv file using function parameters in two modules in PYTHON"}, {"tags": ["python", "rest", "flask", "flask-sqlalchemy", "flask-restful"], "owner": {"reputation": 25, "user_id": 15110863, "user_type": "registered", "profile_image": "https://lh5.googleusercontent.com/-88W6lmFakBc/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuclHkVJFSqBTxxvOX34jP2S--U5jVA/s96-c/photo.jpg?sz=256", "display_name": "SkyDaddy2021", "link": "https://stackoverflow.com/users/15110863/skydaddy2021"}, "is_answered": true, "view_count": 1937, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 68825974, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/68825974/sqlalchemy-exc-operationalerror-sqlite3-operationalerror-unable-to-open-datab", "title": "sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) unable to open database file"}, {"tags": ["python", "python-3.x", "batch-file", "cmd"], "owner": {"reputation": 450, "user_id": 13652624, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1a3de4bf14e19be2f63033b039aa4cf9?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/13652624/frederick"}, "is_answered": true, "view_count": 2192, "accepted_answer_id": 64705835, "answer_count": 1, "score": 4, "last_activity_date": **********, "creation_date": **********, "question_id": 64634045, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/64634045/python-error-just-on-pc-startup-sqlite3-operationalerror-unable-to-open-databas", "title": "Python Error just on PC startup sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "windows", "hive", "sasl"], "owner": {"reputation": 114, "user_id": 147418, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/68fab9bb5ff070ea32ad9299d71d4a17?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/147418/dan"}, "is_answered": false, "view_count": 75, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 69545381, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/69545381/how-to-configure-mechanism-in-thrift-sasl", "title": "How to configure mechanism in thrift_sasl"}, {"tags": ["python", "django", "centos", "mod-wsgi", "centos7"], "owner": {"reputation": 927, "user_id": 5796284, "user_type": "registered", "accept_rate": 94, "profile_image": "https://www.gravatar.com/avatar/0d0c55f94426eb3d52568d5540aab6cf?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "hretic", "link": "https://stackoverflow.com/users/5796284/hretic"}, "is_answered": true, "view_count": 13316, "accepted_answer_id": 40732808, "answer_count": 3, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 40732334, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/40732334/django-error-logging-errno-13-permission-denied", "title": "django error logging : [<PERSON><PERSON><PERSON> 13] Permission denied"}, {"tags": ["python", "selenium", "selenium-webdriver", "microsoft-file-explorer"], "owner": {"reputation": 11, "user_id": 15200591, "user_type": "registered", "profile_image": "https://lh4.googleusercontent.com/-2NG0AqIa1eU/AAAAAAAAAAI/AAAAAAAAAAA/AMZuuclveAV12aFjRdrn_BFllWhzZ_CrKQ/s96-c/photo.jpg?sz=256", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/15200591/killian-sullivan"}, "is_answered": false, "view_count": 535, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 66181171, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/66181171/how-to-have-selenium-select-a-file-from-a-file-explorer-opened-by-a-webpage", "title": "How to have Se<PERSON><PERSON> select a file from a file explorer opened by a webpage"}, {"tags": ["python", "sqlite", "flask", "flask-sqlalchemy"], "owner": {"reputation": 1854, "user_id": 9211026, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/b7c2548cd90c765029e52a7df9ea2fe5?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/9211026/cmrussell"}, "is_answered": true, "view_count": 2191, "answer_count": 2, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 69057406, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/69057406/flask-sqlalchemy-cant-connect-to-database", "title": "Flask SQLAlchemy Can&#39;t Connect to Database"}, {"tags": ["python", "kivy", "kivy-language", "kiv<PERSON>d"], "owner": {"reputation": 3, "user_id": 16779145, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a/AATXAJzdZ7kq3HAgfmVvj8vMbfTqKAPzcMhmKZuEk-FJ=k-s256", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/16779145/vivek-tomar"}, "is_answered": true, "view_count": 150, "accepted_answer_id": 68973026, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 68972331, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/68972331/i-want-to-call-a-screen-from-python-file-in-kivy-builder-format", "title": "I want to call a screen from python file in kivy builder format"}, {"tags": ["python", "database", "django", "sqlite", "django-settings"], "owner": {"reputation": 11768, "user_id": 765357, "user_type": "registered", "accept_rate": 88, "profile_image": "https://i.stack.imgur.com/pFCkr.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/765357/chris"}, "is_answered": true, "view_count": 194269, "accepted_answer_id": 7670618, "answer_count": 7, "score": 61, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 7670289, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/7670289/sqlite3-operationalerror-unable-to-open-database-file", "title": "sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "docker", "docker-compose", "continuous-integration", "pytest"], "owner": {"reputation": 109, "user_id": 10933668, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/bcce2c9ff6642c261565fc43ff02bed0?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Kevimuxx69", "link": "https://stackoverflow.com/users/10933668/kevimuxx69"}, "is_answered": false, "view_count": 2698, "answer_count": 1, "score": 6, "last_activity_date": **********, "creation_date": **********, "question_id": 64138038, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/64138038/docker-sqlite3-operationalerror-unable-to-open-database-file", "title": "Docker sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "api", "authentication", "netsuite", "suitetalk"], "owner": {"reputation": 1889, "user_id": 11796003, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/yysPJ.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/11796003/adam-strauss"}, "is_answered": true, "view_count": 1691, "accepted_answer_id": 68857432, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 65953411, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/65953411/invalid-login-attempt-while-attempting-to-hit-an-api", "title": "Invalid <PERSON>gin Attempt-While attempting to hit an API"}, {"tags": ["python", "automation", "window", "project", "operationalerror"], "owner": {"reputation": 1, "user_id": 16690049, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/54633c4ce865f88757772056d720f329?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "kunal", "link": "https://stackoverflow.com/users/16690049/kunal"}, "is_answered": false, "view_count": 270, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 68820969, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/68820969/operational-error-unable-to-open-database-file-automation", "title": "Operational Error : unable to open database file (automation)"}, {"tags": ["python", "sqlite", "python-dataset"], "owner": {"reputation": 642, "user_id": 1397041, "user_type": "registered", "accept_rate": 47, "profile_image": "https://www.gravatar.com/avatar/6639459190c96887363478d767fe0270?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1397041/layne"}, "is_answered": true, "view_count": 177, "accepted_answer_id": 36320917, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 36319168, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/36319168/how-do-i-get-the-dataset-module-for-python-to-work-when-deployed", "title": "How do I get the dataset module for Python to work when deployed?"}, {"tags": ["python", "sqlite", "path", "aiohttp"], "owner": {"reputation": 3250, "user_id": 8372455, "user_type": "registered", "accept_rate": 71, "profile_image": "https://www.gravatar.com/avatar/6d19cd3875b6dfc524b710c5c81baaa0?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "bbartling", "link": "https://stackoverflow.com/users/8372455/bbartling"}, "is_answered": true, "view_count": 110, "accepted_answer_id": 68750812, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 68744950, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/68744950/web-app-make-db-on-first-run-of-python-code", "title": "web app make db on first run of python code"}, {"tags": ["python", "kubernetes"], "owner": {"reputation": 608, "user_id": 8820616, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/cA4to.jpg?s=256&g=1", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/8820616/prade<PERSON>-padmanaban-c"}, "is_answered": true, "view_count": 769, "accepted_answer_id": 68725376, "answer_count": 2, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 61192254, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61192254/how-to-disable-runasnonroot-in-kubernetes", "title": "How to disable runasNonR<PERSON> in Kubernetes"}, {"tags": ["python", "django"], "owner": {"reputation": 123, "user_id": 15853459, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/4b26332ba940e138f219cc118b4e54f1?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/15853459/rika-das"}, "is_answered": false, "view_count": 337, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 68707963, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/68707963/getting-error-while-try-to-login-in-django-admin-database", "title": "Getting error while try to login in django admin database"}, {"tags": ["python", "xml", "blob"], "owner": {"reputation": 141, "user_id": 2645914, "user_type": "registered", "accept_rate": 40, "profile_image": "https://www.gravatar.com/avatar/57c2f02b1c6aa8af66c1096d70e692eb?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/2645914/krank"}, "is_answered": true, "view_count": 155, "accepted_answer_id": 68322249, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 68321434, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/68321434/extracting-zip-blob-from-xml-to-file-with-python", "title": "Extracting .zip blob from XML to file with python"}, {"tags": ["python", "flask", "sqlalchemy"], "owner": {"reputation": 1, "user_id": 14537421, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/38784c55e329510d9bd3e6bc62de1664?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "mvonherrmann24", "link": "https://stackoverflow.com/users/14537421/mvonherrmann24"}, "is_answered": false, "view_count": 363, "answer_count": 2, "score": -2, "last_activity_date": **********, "creation_date": **********, "question_id": 64596544, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/64596544/unable-to-open-database-file-error-creating-login-page-using-flask-sqlalchemy", "title": "Unable to open database file error - creating login page using flask sqlalchemy"}, {"tags": ["python", "sqlite", "flask", "sqlalchemy"], "owner": {"reputation": 3, "user_id": 16351778, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1ca4af8df8314acd48e5975a753b123d?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Tix00", "link": "https://stackoverflow.com/users/16351778/tix00"}, "is_answered": true, "view_count": 48, "accepted_answer_id": 68198526, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 68198304, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/68198304/python-sqlalchemy-flask-sqlite-can-open-database-only-from-python-crud-py-whe", "title": "Python SQLAlchemy Flask Sqlite can open database only from &quot;python crud.py&quot;. When imported while the app is running, it cant open the database"}, {"tags": ["python", "postgresql", "pyqt5"], "owner": {"reputation": 109, "user_id": 1943612, "user_type": "registered", "accept_rate": 40, "profile_image": "https://www.gravatar.com/avatar/fcbba89c78e42878a4f1a5dcf3d6663a?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1943612/rich-shepard"}, "is_answered": false, "view_count": 81, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 67855236, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67855236/pyqt5-qsql-application-not-finding-postgres-table", "title": "PyQt5/QSql: application not finding postgres table"}, {"tags": ["python", "flask", "flask-sqlalchemy", "pyinstaller"], "owner": {"reputation": 73, "user_id": 13363952, "user_type": "registered", "profile_image": "https://lh5.googleusercontent.com/-e2HlJPkMyHA/AAAAAAAAAAI/AAAAAAAAAAA/AAKWJJN28T0lCtYJrqomy8ITfKro3FTN4A/photo.jpg?sz=256", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/13363952/idanah"}, "is_answered": false, "view_count": 1378, "answer_count": 0, "score": 6, "last_activity_date": **********, "creation_date": **********, "question_id": 63390028, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63390028/converting-flask-web-app-to-a-standalone-executable-desktop-app", "title": "Converting flask web app to a standalone executable desktop app"}, {"tags": ["python", "multithreading", "docker"], "owner": {"reputation": 3551, "user_id": 335427, "user_type": "registered", "accept_rate": 74, "profile_image": "https://www.gravatar.com/avatar/107ac6beacf71f2a5d4165d5bdc2cedb?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/335427/chris"}, "is_answered": false, "view_count": 1146, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 67874487, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67874487/threads-unable-to-open-database-file-with-sqlite3-inside-docker", "title": "Threads: unable to open database file with sqlite3 inside docker"}, {"tags": ["python", "django", "docker", "jenkins", "selenium-webdriver"], "owner": {"reputation": 11, "user_id": 14243711, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/lUoL6.png?s=256&g=1", "display_name": "Eilon285", "link": "https://stackoverflow.com/users/14243711/eilon285"}, "is_answered": false, "view_count": 577, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 67807054, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67807054/django-selenium-test-works-in-local-docker-container-but-not-on-jenkins-server", "title": "Django Selenium test works in local Docker container but not on Jenkins server"}, {"tags": ["python", "flask", "sqlalchemy"], "owner": {"reputation": 179, "user_id": 14508690, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/0784d33fea9917a4fa4bfb82e2243d60?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "101is5", "link": "https://stackoverflow.com/users/14508690/101is5"}, "is_answered": false, "view_count": 949, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 67745062, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67745062/python-flask-sqlalchemy-unable-to-open-database-file-with-sqlite-tmp-test", "title": "(Python/Flask_SQLAlchemy) Unable to open database file with &#39;sqlite:////tmp/test.db&#39;"}, {"tags": ["python", "sqlite", "flask", "sqlalchemy"], "owner": {"reputation": 57, "user_id": 4406222, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/befe461d3f1e9963fbdc7da71ad82a63?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "gdv19", "link": "https://stackoverflow.com/users/4406222/gdv19"}, "is_answered": true, "view_count": 389, "accepted_answer_id": 67729755, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 67729319, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67729319/sql-alchemy-unable-to-open-database-file-with-multiple-databases", "title": "sql alchemy unable to open database file with multiple databases"}, {"tags": ["python", "flask", "sqlalchemy", "apache2"], "owner": {"reputation": 855, "user_id": 12920146, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/abROU.gif?s=256&g=1", "display_name": "Emir S&#252;rmen", "link": "https://stackoverflow.com/users/12920146/emir-s%c3%bcrmen"}, "is_answered": true, "view_count": 334, "accepted_answer_id": 67725796, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 67605681, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67605681/apache2-wsgi-flask-app-gives-sqlalchemy-exc-operationalerror-sqlite3-operation", "title": "Apache2 WSGI Flask app gives sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) unable to open database file"}, {"tags": ["python", "flask", "sqlalchemy"], "owner": {"reputation": 53, "user_id": 10827487, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/7e3180c82928fbad6028de65fcd3f708?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Andrei GH", "link": "https://stackoverflow.com/users/10827487/andrei-gh"}, "is_answered": false, "view_count": 62, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 67694578, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67694578/i-got-an-error-when-i-try-to-login-using-flask-form", "title": "I got an error when I try to login using Flask form"}, {"tags": ["python", "django", "nginx", "sqlite", "gunicorn"], "owner": {"reputation": 1544, "user_id": 2202642, "user_type": "registered", "accept_rate": 86, "profile_image": "https://www.gravatar.com/avatar/696dc04b60bbf6ac16bd2bc16ccac191?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "DWB", "link": "https://stackoverflow.com/users/2202642/dwb"}, "is_answered": true, "view_count": 6580, "accepted_answer_id": 26279164, "answer_count": 2, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 26275834, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/26275834/django-admin-backend-operational-error-attempt-to-write-a-readonly-database", "title": "Django admin backend &#39;Operational Error&#39; attempt to write a readonly database"}, {"tags": ["python", "python-3.x", "imessage"], "owner": {"reputation": 11, "user_id": 13910414, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/be53633c49eb823c07c6b1e68fa3fa10?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "exotic", "link": "https://stackoverflow.com/users/13910414/exotic"}, "is_answered": false, "view_count": 650, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 63022424, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63022424/why-does-the-imessage-program-throw-a-database-error", "title": "Why does the iMessage program throw a database error?"}, {"tags": ["python", "flask", "<PERSON><PERSON>", "sqlalchemy"], "owner": {"reputation": 23, "user_id": 13374271, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AOh14GhwGzi4x8jKjG87vQPhcntltPTrHNV_2GBQ1f65=k-s256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/13374271/zack"}, "is_answered": false, "view_count": 277, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 67393960, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67393960/flask-app-returns-500-error-after-heroku-deployment", "title": "Flask app returns 500 error after Heroku deployment"}, {"tags": ["python", "django", "httprequest", "esp8266"], "owner": {"reputation": 45, "user_id": 11968611, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/e69bb7648b0296b31d0b1063a4009fc5?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/11968611/arsh"}, "is_answered": false, "view_count": 281, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 67400480, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67400480/esp8266-not-connecting-to-django-server", "title": "ESP8266 not connecting to django server"}, {"tags": ["python", "sqlite", "csv"], "owner": {"reputation": 3873, "user_id": 11401460, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/8d6f2c239c82a2c4868dcd98c5055f76?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "uber", "link": "https://stackoverflow.com/users/11401460/uber"}, "is_answered": true, "view_count": 574, "accepted_answer_id": 67302614, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 67300972, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67300972/create-a-sqlite-table-from-a-csv-file", "title": "Create a sqlite table from a csv file"}, {"tags": ["python"], "owner": {"reputation": 642, "user_id": 7929423, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/947f7813b6eabc00924b49f065815f4f?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "cluis92", "link": "https://stackoverflow.com/users/7929423/cluis92"}, "is_answered": false, "view_count": 142, "answer_count": 2, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 67060064, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/67060064/how-to-connect-to-sqlite3-database-using-python", "title": "How to connect to sqlite3 database using python"}, {"tags": ["python", "python-3.x", "unit-testing", "pytest", "code-coverage"], "owner": {"reputation": 10177, "user_id": 1011253, "user_type": "registered", "accept_rate": 76, "profile_image": "https://i.stack.imgur.com/NAA1r.jpg?s=256&g=1", "display_name": "ItayB", "link": "https://stackoverflow.com/users/1011253/itayb"}, "is_answered": true, "view_count": 622, "accepted_answer_id": 66971055, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 66945104, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/66945104/coverage-internalerror-due-to-mocking-in-pytest", "title": "coverage INTERNALERROR due to Mocking in pytest"}, {"tags": ["python", "sqlite", "jupyter-notebook"], "owner": {"reputation": 303, "user_id": 11637971, "user_type": "registered", "profile_image": "https://lh6.googleusercontent.com/-0t7ArHUfmZk/AAAAAAAAAAI/AAAAAAAAABA/VsjHnOR-y7k/photo.jpg?sz=256", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/11637971/jackeyol"}, "is_answered": false, "view_count": 200, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 66702339, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/66702339/allow-full-disk-access-in-jupyter-notebook", "title": "allow full disk access in Jupy<PERSON> notebook"}, {"tags": ["python", "flask", "iis", "plotly-dash", "wfastcgi"], "owner": {"reputation": 155, "user_id": 5203899, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/a0070d746664d3f87ed94b121bb688a2?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Umut885", "link": "https://stackoverflow.com/users/5203899/umut885"}, "is_answered": false, "view_count": 1623, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 58980691, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/58980691/cant-get-dash-app-run-on-iis-with-flask-server", "title": "Can&#39;t get dash app run on IIS with flask server"}, {"tags": ["python", "database", "sqlite", "ubuntu", "flask"], "owner": {"reputation": 59, "user_id": 14988187, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/9955e9b61d2fdc18fdef54b6f879a35a?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Python 123", "link": "https://stackoverflow.com/users/14988187/python-123"}, "is_answered": false, "view_count": 251, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 66487134, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/66487134/sqlite-connection-working-in-one-python-file-but-not-in-another", "title": "Sqlite connection working in one python file but not in another"}, {"tags": ["python", "django", "linux", "database", "python-3.x"], "owner": {"reputation": 322, "user_id": 7335183, "user_type": "registered", "accept_rate": 100, "profile_image": "https://graph.facebook.com/10202803887574250/picture?type=large", "display_name": "Nikola C", "link": "https://stackoverflow.com/users/7335183/nikola-c"}, "is_answered": true, "view_count": 676, "accepted_answer_id": 52485667, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 52479133, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/52479133/unable-to-open-database-file-mayan-edms", "title": "Unable to open database file - Mayan EDMS"}, {"tags": ["python", "database", "sqlite"], "owner": {"reputation": 1, "user_id": 13085427, "user_type": "registered", "profile_image": "https://graph.facebook.com/1928250407305935/picture?type=large", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/13085427/amir-mousawi"}, "is_answered": false, "view_count": 26, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 65412964, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/65412964/whats-going-wrong-with-my-sqlite3-database", "title": "What&#39;s going wrong with my sqlite3 database?"}, {"tags": ["python", "git", "<PERSON><PERSON>"], "owner": {"reputation": 137, "user_id": 3834765, "user_type": "registered", "accept_rate": 0, "profile_image": "https://i.stack.imgur.com/5TXv5.png?s=256&g=1", "display_name": "lennys", "link": "https://stackoverflow.com/users/3834765/lennys"}, "is_answered": false, "view_count": 309, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 65050931, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/65050931/heroku-cant-find-sdl-config", "title": "<PERSON><PERSON> can&#39;t find sdl-config?"}, {"tags": ["python", "apache", "ubuntu", "flask", "mod-wsgi"], "owner": {"reputation": 1187, "user_id": 1408484, "user_type": "registered", "accept_rate": 85, "profile_image": "https://www.gravatar.com/avatar/929b355c066001c5c9f2154929da597e?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Man8Blue", "link": "https://stackoverflow.com/users/1408484/man8blue"}, "is_answered": true, "view_count": 18313, "accepted_answer_id": 18221698, "answer_count": 4, "score": 9, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 17641993, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/17641993/operationalerror-attempt-to-write-a-readonly-database-in-ubuntu-server", "title": "OperationalError: attempt to write a readonly database in ubuntu server"}, {"tags": ["python", "amazon-web-services", "sqlalchemy", "ubuntu-18.04"], "owner": {"reputation": 91, "user_id": 8921969, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/59d0f9f0bc5680686507185218eb4e6a?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "midav", "link": "https://stackoverflow.com/users/8921969/midav"}, "is_answered": true, "view_count": 8857, "closed_date": **********, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 58175162, "link": "https://stackoverflow.com/questions/58175162/sqlalchemy-exc-operationalerror-sqlite3-operationalerror-unable-to-open-datab", "closed_reason": "Duplicate", "title": "sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) unable to open database file error when trying to create a database"}, {"tags": ["python", "c++", "vim", "youcompleteme", "clangd"], "owner": {"reputation": 43, "user_id": 12555300, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/ae0325b793c64e0d2e0c13c45ba75e35?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/12555300/grizzlyman"}, "is_answered": false, "view_count": 412, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 64944586, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/64944586/ycm-cant-find-header-situated-in-another-directory", "title": "ycm can&#39;t find header situated in another directory"}, {"tags": ["python", "sqlite", "module", "sqlalchemy"], "owner": {"reputation": 460, "user_id": 10149318, "user_type": "registered", "profile_image": "https://lh6.googleusercontent.com/--zpevkWzm7c/AAAAAAAAAAI/AAAAAAAAA5o/7Dkw8RB-BpE/photo.jpg?sz=256", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/10149318/shervin-rad"}, "is_answered": true, "view_count": 30, "closed_date": **********, "accepted_answer_id": 64720757, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 64720554, "link": "https://stackoverflow.com/questions/64720554/does-importing-a-class-from-a-module-execute-the-entire-module-on-import-python", "closed_reason": "Duplicate", "title": "Does importing a class from a module execute the entire module on import? python 3"}, {"tags": ["python", "django", "django-models", "django-forms", "django-views"], "owner": {"reputation": 136, "user_id": 13817431, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/27382f1ab0d20ca159752b4418eed05a?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/13817431/amit-saini"}, "is_answered": true, "view_count": 1082, "accepted_answer_id": 64415515, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 64415489, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/64415489/how-to-solve-leadform-object-has-no-attribute-save-error-in-django", "title": "How to Solve &#39;LeadForm&#39; object has no attribute &#39;save&#39; error in Django?"}, {"tags": ["python", "django", "postgresql", "python-venv"], "owner": {"reputation": 261, "user_id": 5231926, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/03mkE.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/5231926/leslie-joe"}, "is_answered": false, "view_count": 498, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 64399105, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/64399105/installing-pycopg2-gave-me-an-issue", "title": "Installing pycopg2 gave me an issue"}, {"tags": ["python", "server", "python-requests", "locust"], "owner": {"reputation": 447, "user_id": 1373924, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1a274fd31723030643fa96aaead1175f?s=256&d=identicon&r=PG", "display_name": "arcane", "link": "https://stackoverflow.com/users/1373924/arcane"}, "is_answered": true, "view_count": 2465, "answer_count": 1, "score": 25, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 31600508, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/31600508/locust-io-load-testing-getting-connection-aborted-badstatusline-errors", "title": "Locust.io Load Testing getting &quot;Connection aborted BadStatusLine&quot; Errors"}, {"tags": ["python", "sql", "apache", "flask", "ubuntu-server"], "owner": {"reputation": 47, "user_id": 12374945, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/eZNrn.png?s=256&g=1", "display_name": "OnlyTrueJames", "link": "https://stackoverflow.com/users/12374945/onlytruejames"}, "is_answered": false, "view_count": 382, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 63849759, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63849759/flask-not-opening-sqlite-database-on-apache2", "title": "Flask not opening SQLite Database on apache2"}, {"tags": ["python", "database", "macos", "sqlite", "atom-editor"], "owner": {"reputation": 33, "user_id": 13683189, "user_type": "registered", "profile_image": "https://lh5.googleusercontent.com/-DpSs_ecyWNY/AAAAAAAAAAI/AAAAAAAAAAA/AMZuucl7GwZLPmKAcpIc2pELVLlrhPtsRw/photo.jpg?sz=256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/13683189/pablo"}, "is_answered": false, "view_count": 96, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 63716164, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63716164/python-sqlite3-unable-to-open-database-on-atom", "title": "Python sqlite3 unable to open database on atom"}, {"tags": ["python", "pyqt5"], "owner": {"reputation": 13, "user_id": 14015160, "user_type": "registered", "profile_image": "https://lh5.googleusercontent.com/-8C4a6h02rU0/AAAAAAAAAAI/AAAAAAAAAAA/AMZuucky-QarPyPaJL52qyjHX5KGh<PERSON>eegg/photo.jpg?sz=256", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/14015160/ve<PERSON><PERSON>-panov"}, "is_answered": true, "view_count": 133, "accepted_answer_id": 63448564, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 63448266, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63448266/how-i-can-align-values-exported-from-database-with-query-in-pyqt5-table-view", "title": "How I can align values exported from database with query in PyQt5 table view"}, {"tags": ["python", "php", "mysql", "raspberry-pi3"], "owner": {"reputation": 1, "user_id": 10527578, "user_type": "registered", "profile_image": "https://lh4.googleusercontent.com/-JAt-pzcnwI4/AAAAAAAAAAI/AAAAAAAAAAA/AAN31DWZd7tAKzKa6O9kgbLIGzWyq2hJBA/mo/photo.jpg?sz=256", "display_name": "K173091 <PERSON>", "link": "https://stackoverflow.com/users/10527578/k173091-syed-rizvi"}, "is_answered": false, "view_count": 294, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 63372021, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63372021/how-to-run-raspberry-pi-3-b-image-in-virtualbox-or-any-other-simulation-such-as", "title": "How to run raspberry pi 3 B+ image in VirtualBox or any other Simulation such as Qemu"}, {"tags": ["python", "installation", "pip", "cairo", "python-cffi"], "owner": {"reputation": 113566, "user_id": 610569, "user_type": "registered", "accept_rate": 90, "profile_image": "https://www.gravatar.com/avatar/0e9087f2672b0e4f28d91266acf9ce57?s=256&d=identicon&r=PG", "display_name": "alvas", "link": "https://stackoverflow.com/users/610569/alvas"}, "is_answered": true, "view_count": 27496, "accepted_answer_id": 29596525, "answer_count": 3, "score": 16, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 29596426, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/29596426/how-to-pip-install-cairocffi", "title": "How to pip install ca<PERSON><PERSON><PERSON><PERSON>?"}, {"tags": ["python", "networking", "ssh", "<PERSON><PERSON><PERSON>", "fabric"], "owner": {"reputation": 150, "user_id": 12019063, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/8d4db57d65808cc607f881ca268e2b36?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "elmo26", "link": "https://stackoverflow.com/users/12019063/elmo26"}, "is_answered": true, "view_count": 632, "accepted_answer_id": 63360100, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 63357980, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63357980/python3-fabric-ssh-tunnel-to-access-database-on-remote-server-paramiko-ssh-exce", "title": "Python3 Fabric ssh tunnel to access database on remote server [paramiko.ssh_exception.NoValidConnectionsError]"}, {"tags": ["python", "file-permissions", "errno", "operationalerror"], "owner": {"reputation": 1, "user_id": 14056271, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/5ffa6c2de7744dde8c4771132b71d627?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/14056271/daniel-carcamo"}, "is_answered": false, "view_count": 692, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 63272993, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63272993/errno-1-operation-not-permitted-but-file-has-full-permissions", "title": "Errno 1 Operation not permitted. But file has full permissions?"}, {"tags": ["python", "memory", "subprocess", "zip", "postgis"], "owner": {"reputation": 53, "user_id": 4123882, "user_type": "registered", "accept_rate": 100, "profile_image": "https://www.gravatar.com/avatar/2d217504d7bba9867f087966f549a75b?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "MapPeddler", "link": "https://stackoverflow.com/users/4123882/mappeddler"}, "is_answered": false, "view_count": 219, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 63182328, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63182328/can-i-unzip-downloaded-zipped-shapefiles-into-memory-for-upload-into-database", "title": "Can I unzip downloaded zipped shapefiles into memory for upload into database?"}, {"tags": ["python", "sql", "ms-access", "pyodbc", "readonly"], "owner": {"reputation": 1553, "user_id": 10849457, "user_type": "registered", "profile_image": "https://lh4.googleusercontent.com/-yJgV9iy_jz0/AAAAAAAAAAI/AAAAAAAAADo/FdDaLrk8-cY/photo.jpg?sz=256", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/10849457/vignesh"}, "is_answered": true, "view_count": 831, "accepted_answer_id": 63174880, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 63172861, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/63172861/read-only-method-for-querying-an-access-database-mdb-file", "title": "Read only method for querying an Access database (.mdb) file?"}, {"tags": ["python", "django", "docker", "docker-compose", "code-coverage"], "owner": {"reputation": 1754, "user_id": 11440526, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/50HkQ.jpg?s=256&g=1", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/11440526/mwibutsa-floribert"}, "is_answered": true, "view_count": 4388, "answer_count": 2, "score": 5, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 61999735, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61999735/sqlite3-operationalerror-unable-to-open-database-file-when-running-ddango-test", "title": "sqlite3.OperationalError: unable to open database file when running dDango test coverage inside a docker container"}, {"tags": ["python", "ms-access", "sqlalchemy", "pyodbc", "sqlalchemy-access"], "owner": {"reputation": 80, "user_id": 11277192, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/GAzCc.jpg?s=256&g=1", "display_name": "Tfmr_uk", "link": "https://stackoverflow.com/users/11277192/tfmr-uk"}, "is_answered": true, "view_count": 281, "accepted_answer_id": 62934130, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 62877914, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/62877914/read-a-zip-file-from-url-and-convert-a-ms-access-file-to-data-frame", "title": "Read a Zip file from URL and convert a Ms Access file to data frame"}, {"tags": ["python", "sqlite", "sqlalchemy"], "owner": {"reputation": 49, "user_id": 11308842, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/db4447f820a6c32eb23cec920ce7f8bd?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "bhylynch98", "link": "https://stackoverflow.com/users/11308842/bhylynch98"}, "is_answered": false, "view_count": 157, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 62905092, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/62905092/what-is-the-method-to-create-a-sqlalchemy-engine-for-a-db-file-rather-than-a-sq", "title": "What is the method to create a sqlalchemy engine for a .db file rather than a sqlite file?"}, {"tags": ["python", "flask", "raspberry-pi4", "adafruit"], "owner": {"reputation": 11, "user_id": 3496049, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/4f5d5b46d51c44294f1d1f38029fb749?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user3496049", "link": "https://stackoverflow.com/users/3496049/user3496049"}, "is_answered": false, "view_count": 496, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 62854775, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/62854775/reading-dht22-from-flask-murders-gpio", "title": "Reading DHT22 from flask murders gpio"}, {"tags": ["python", "sqlite", "flask", "sqlalchemy"], "owner": {"reputation": 19, "user_id": 13492134, "user_type": "registered", "profile_image": "https://graph.facebook.com/2804488772982772/picture?type=large", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/13492134/callum-wallace"}, "is_answered": true, "view_count": 121, "accepted_answer_id": 62730908, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 62730875, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/62730875/linking-a-simple-sqlite-database-using-flask", "title": "Linking a simple SQLite Database using Flask"}, {"tags": ["python", "postgresql", "csv", "ms-access", "32bit-64bit"], "owner": {"reputation": 33, "user_id": 8697912, "user_type": "registered", "profile_image": "https://lh4.googleusercontent.com/-co8C-UTOKqI/AAAAAAAAAAI/AAAAAAAAADg/alx76-vna0I/photo.jpg?sz=256", "display_name": "HC Pieck", "link": "https://stackoverflow.com/users/8697912/hc-pieck"}, "is_answered": true, "view_count": 2643, "accepted_answer_id": 62410484, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 48539500, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/48539500/how-to-read-from-a-32-bit-mdb-with-64-bit-python-and-odbc-driver", "title": "How to read from a 32 bit .mdb with 64 bit python and odbc driver"}, {"tags": ["python", "oracle11g", "sqlalchemy", "cx-oracle"], "owner": {"reputation": 1037, "user_id": 4851554, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/22c1ea791a3e02714089ff9bfffafe00?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/4851554/arnab"}, "is_answered": false, "view_count": 2080, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 62344275, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/62344275/cannot-locate-a-64-bit-oracle-client-library", "title": "Cannot locate a 64-bit Oracle Client library"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 2549, "user_id": 2494602, "user_type": "registered", "accept_rate": 89, "profile_image": "https://i.stack.imgur.com/OZlRo.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/2494602/dirk-calloway"}, "is_answered": true, "view_count": 3289, "accepted_answer_id": 23471747, "answer_count": 1, "score": 12, "last_activity_date": **********, "creation_date": **********, "question_id": 23471383, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/23471383/cant-open-sqlite-db-create-in-python-with-sqlite-command-bash", "title": "Can&#39;t open Sqlite db create in Python with sqlite command bash"}, {"tags": ["python", "exec", "file-descriptor"], "owner": {"reputation": 13161, "user_id": 344143, "user_type": "registered", "accept_rate": 89, "profile_image": "https://www.gravatar.com/avatar/4bd01877738ff2b917d80e750eefb449?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/344143/ben-mosher"}, "is_answered": true, "view_count": 8945, "answer_count": 2, "score": 3, "last_activity_date": **********, "creation_date": **********, "question_id": 44950592, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/44950592/python-2-7-popen-what-does-close-fds-do", "title": "python 2.7 <PERSON><PERSON>: what does `close_fds` do?"}, {"tags": ["python", "database", "amazon-web-services", "sqlite"], "owner": {"reputation": 36, "user_id": 7671967, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/pwUEw.png?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/7671967/emanuel-amit"}, "is_answered": true, "view_count": 2472, "accepted_answer_id": 44090447, "answer_count": 2, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 44090121, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/44090121/in-memory-sqlite3-shared-database-python", "title": "In memory SQLite3 shared database python"}, {"tags": ["python", "sqlite", "ubuntu", "deployment", "apache2"], "owner": {"reputation": 19, "user_id": 12858699, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/-xkj-68b1dyA/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3reyUVUK7iu4ujRm24MQ-S6_COUBhw/photo.jpg?sz=256", "display_name": "Dom", "link": "https://stackoverflow.com/users/12858699/dom"}, "is_answered": false, "view_count": 31, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 61799978, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61799978/why-can-my-flask-app-no-longer-access-my-sqlite3-dabatase-after-being-moved-to-a", "title": "Why can my flask app no longer access my SQLite3 dabatase after being moved to a Ubuntu virtual machine?"}, {"tags": ["python", "jupyter-notebook"], "owner": {"reputation": 17615, "user_id": 2063031, "user_type": "registered", "accept_rate": 96, "profile_image": "https://www.gravatar.com/avatar/f2677db7242bfbcead46fbf27390160d?s=256&d=identicon&r=PG", "display_name": "ostrokach", "link": "https://stackoverflow.com/users/2063031/ostrokach"}, "is_answered": true, "view_count": 1918, "answer_count": 2, "score": 4, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 58665591, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/58665591/jupyter-fails-to-open-notebook-with-error-file-xx-has-unsaved-changes-close", "title": "<PERSON><PERSON><PERSON> fails to open notebook with error: &#39;File &quot;XX&quot; has unsaved changes, close without saving?&#39;"}, {"tags": ["python", "database"], "owner": {"reputation": 41, "user_id": 11268313, "user_type": "registered", "profile_image": "https://lh5.googleusercontent.com/-VsqlMEhEpTY/AAAAAAAAAAI/AAAAAAAADWQ/GDrhlf1Xa18/photo.jpg?sz=256", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/11268313/asma-elbiltagy"}, "is_answered": false, "view_count": 40, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 61391579, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61391579/unable-to-open-database-files", "title": "Unable to open database files"}, {"tags": ["python", "sqlite", "terminal", "sqlalchemy"], "owner": {"reputation": 11768, "user_id": 765357, "user_type": "registered", "accept_rate": 88, "profile_image": "https://i.stack.imgur.com/pFCkr.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/765357/chris"}, "is_answered": true, "view_count": 23104, "accepted_answer_id": 7306123, "answer_count": 3, "score": 25, "last_activity_date": **********, "creation_date": **********, "question_id": 7302619, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/7302619/operationalerror-operationalerror-unable-to-open-database-file-none-none", "title": "OperationalError: (OperationalError) unable to open database file None None"}, {"tags": ["python", "regex", "stanford-nlp", "named-entity-recognition"], "owner": {"reputation": 932, "user_id": 2160884, "user_type": "registered", "accept_rate": 56, "profile_image": "https://www.gravatar.com/avatar/923672887ad98e67777f9a400167fa8d?s=256&d=identicon&r=PG", "display_name": "sel", "link": "https://stackoverflow.com/users/2160884/sel"}, "is_answered": false, "view_count": 851, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 61235575, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61235575/stanford-corenlp-tokensregex-error-while-parsing-the-rules-file-in-python", "title": "Stanford CoreNLP TokensRegex / Error while parsing the .rules file in Python"}, {"tags": ["python", "sqlite", "unit-testing", "flask", "python-unittest"], "owner": {"reputation": 3, "user_id": 13304070, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AOh14GhtJBNmig1rVMy9Ac7vNStWME8oLbO_d_Ec2QXs=k-s256", "display_name": "alawal98", "link": "https://stackoverflow.com/users/13304070/alawal98"}, "is_answered": true, "view_count": 328, "accepted_answer_id": 61196865, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 61193428, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61193428/flask-unit-test-cant-connect-to-sqlite-database-when-running-tests", "title": "Flask unit test can&#39;t connect to sqlite database when running tests"}, {"tags": ["python", "django-rest-framework", "python-requests"], "owner": {"reputation": 104, "user_id": 13281878, "user_type": "registered", "profile_image": "https://lh4.googleusercontent.com/-lW-6sqQkwys/AAAAAAAAAAI/AAAAAAAAAAA/AAKWJJOe-r-YOE_HklX1_bY3vW9JbNzJlQ/photo.jpg?sz=256", "display_name": "<PERSON><PERSON> <PERSON>", "link": "https://stackoverflow.com/users/13281878/raza-ul-mustafa"}, "is_answered": true, "view_count": 112, "accepted_answer_id": 61194110, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 61147626, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61147626/unable-to-store-all-the-data-into-django-database-using-python-requests", "title": "Unable to store all the data into Django database, using Python Requests"}, {"tags": ["python", "linux", "ubuntu", "flask", "sqlalchemy"], "owner": {"reputation": 13, "user_id": 10969432, "user_type": "registered", "profile_image": "https://lh6.googleusercontent.com/-VQG_5O0i6f0/AAAAAAAAAAI/AAAAAAAAAAk/zwZRaOWT6CE/photo.jpg?sz=256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/10969432/bob-smith"}, "is_answered": true, "view_count": 462, "accepted_answer_id": 61163800, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 61163686, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61163686/still-cannot-get-the-apache-server-get-access-to-database-after-added-www-data-t", "title": "still cannot get the apache server get access to database after added www-data to certain group"}, {"tags": ["python", "flask", "executable"], "owner": {"reputation": 831, "user_id": 12946401, "user_type": "registered", "profile_image": "https://lh6.googleusercontent.com/-PoPPawH7Q8I/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rc_jd3ZD1rUojVqtblkSworwbx_ww/photo.jpg?sz=256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/12946401/jeff-boker"}, "is_answered": false, "view_count": 2935, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 61179233, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61179233/creating-single-flask-executable-file-with-pyinstaller", "title": "Creating Single Flask executable file with Pyinstaller"}, {"tags": ["python", "docker", "sqlalchemy", "flask-sqlalchemy", "docker-volume"], "owner": {"reputation": 3298, "user_id": 2034750, "user_type": "registered", "accept_rate": 61, "profile_image": "https://i.stack.imgur.com/eMjN0.jpg?s=256&g=1", "display_name": "151291", "link": "https://stackoverflow.com/users/2034750/151291"}, "is_answered": false, "view_count": 1325, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 61106345, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/61106345/how-to-connect-docker-volume-database-app-db-to-sqlalchemy", "title": "How to connect docker volume &#39;database/app.db&#39; to SQLAlchemy?"}, {"tags": ["python", "database", "sqlite"], "owner": {"reputation": 3958, "user_id": 210631, "user_type": "registered", "accept_rate": 80, "profile_image": "https://www.gravatar.com/avatar/8d1136197dc53001756118e5f97a8de7?s=256&d=identicon&r=PG", "display_name": "Mc-", "link": "https://stackoverflow.com/users/210631/mc"}, "is_answered": true, "view_count": 13317, "accepted_answer_id": 11807721, "answer_count": 4, "score": 8, "last_activity_date": **********, "creation_date": **********, "question_id": 11807700, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/11807700/python-sqlite-not-saving-results-on-the-file", "title": "Python, Sqlite not saving results on the file"}, {"tags": ["python", "apache", "flask", "wsgi", "peewee"], "owner": {"reputation": 50, "user_id": 12989046, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AOh14GhH_3kdA8iGGxkMCjFjCS6-UstHAqAowx87F6dVRw=k-s256", "display_name": "Bajjjjj", "link": "https://stackoverflow.com/users/12989046/bajjjjj"}, "is_answered": true, "view_count": 90, "accepted_answer_id": 60494484, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 60477091, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/60477091/apache-running-flask-on-gcloud-500-internal-server-error", "title": "Apache running Flask on gcloud 500 Internal Server Error"}, {"tags": ["python", "selenium", "selenium-webdriver", "selenium-chromedriver", "pageloadstrategy"], "owner": {"reputation": 562, "user_id": 7940996, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/7e2adf3127f365b0b5addcc08337d77c?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/7940996/manik"}, "is_answered": false, "view_count": 2429, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 59901085, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/59901085/switch-pageloadstrategy-from-eager-to-normal-for-existing-driver", "title": "Switch pageLoadStrategy from eager to normal for existing driver"}, {"tags": ["python", "r", "django", "virtualenv", "gunicorn"], "owner": {"reputation": 225, "user_id": 623721, "user_type": "registered", "accept_rate": 67, "profile_image": "https://www.gravatar.com/avatar/fee2d57cf1c17a678e40c16cbf927ab7?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/623721/talonsensei"}, "is_answered": false, "view_count": 792, "answer_count": 2, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 51353394, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/51353394/gunicorn-cannot-find-system-renviron-error", "title": "Gunicorn cannot find system Renviron error"}, {"tags": ["python", "apache", "sqlite", "flask", "sqlalchemy"], "owner": {"reputation": 113, "user_id": 11901509, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/-JOO2unUGBIQ/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3reIZs7BOL1UAEsi1xOKLeQfJpcnqw/photo.jpg?sz=256", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>l", "link": "https://stackoverflow.com/users/11901509/ab<PERSON><PERSON><PERSON>-khalil"}, "is_answered": false, "view_count": 499, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 59608385, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/59608385/apache-flask-sqlite-unable-to-open-database-file-on-ubuntu", "title": "apache flask sqlite unable to open database file on ubuntu"}, {"tags": ["python", "pandas", "sqlite", "sqlalchemy"], "owner": {"reputation": 691, "user_id": 1704227, "user_type": "registered", "accept_rate": 41, "profile_image": "https://www.gravatar.com/avatar/98e091d359f91dce1da0ad768c2ba589?s=256&d=identicon&r=PG", "display_name": "Definity", "link": "https://stackoverflow.com/users/1704227/definity"}, "is_answered": true, "view_count": 1616, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 59441675, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/59441675/saving-a-pandas-dataframe-to-sqlite3-db-with-sqlalchemy-engine", "title": "Saving a Pandas dataframe to SQLite3 DB with SQLAlchemy engine"}, {"tags": ["python", "sql", "python-3.x", "ms-access", "pyodbc"], "owner": {"reputation": 21, "user_id": 11678301, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/998355169100daf9748e428fd14c9efa?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "pcopp", "link": "https://stackoverflow.com/users/11678301/pcopp"}, "is_answered": false, "view_count": 68, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 59416757, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/59416757/pyodbc-access-only-runs-basic-select-queries", "title": "PyODBC Access only Runs basic Select Queries"}, {"tags": ["python", "database", "sqlite", "connection"], "owner": {"reputation": 7, "user_id": 10376347, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1c7bf9f9ddde08b8d5a07a3702bcbec1?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/10376347/emoo"}, "is_answered": true, "view_count": 3005, "accepted_answer_id": 59340249, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 59339600, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/59339600/cannot-access-an-sqlite3-database-file-with-python", "title": "Cannot access an sqlite3 database file with python"}, {"tags": ["python", "ms-access", "pyodbc"], "owner": {"reputation": 5, "user_id": 12247182, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/a-/AAuE7mCc_qVCvjTyS_dvg2dzHcv-LkyXH6bZqpG8yU7Z=k-s256", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/12247182/khang-nguyen"}, "is_answered": false, "view_count": 111, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 58769348, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/58769348/both-of-my-python-and-ms-access-versions-are-32-bit-but-they-wont-connect-how", "title": "Both of my python and ms-access versions are 32-bit, but they won&#39;t connect. How to connect the two?"}, {"tags": ["python", "sql", "python-3.x", "sqlite", "temporary-files"], "owner": {"reputation": 33, "user_id": 12298763, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/6b49249e1a35537f31ae8e3bd11cb0b2?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "YS-ENB", "link": "https://stackoverflow.com/users/12298763/ys-enb"}, "is_answered": true, "view_count": 1521, "accepted_answer_id": 58632790, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "question_id": 58630687, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/58630687/tempfile-temporaryfile-does-not-work-with-sqlite3-after-being-assigned-to-a-va", "title": "tempfile.TemporaryFile() does not work with sqlite3 after being assigned to a variable"}, {"tags": ["python", "sql", "teradata", "dotenv"], "owner": {"reputation": 323, "user_id": 2740563, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/VeFZ4.jpg?s=256&g=1", "display_name": "ymzkala", "link": "https://stackoverflow.com/users/2740563/ymzkala"}, "is_answered": true, "view_count": 7097, "accepted_answer_id": 58270814, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 58261239, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/58261239/teradatasql-python-module-only-works-when-scripting-but-not-when-running-code", "title": "teradatasql Python module only works when scripting but not when running code"}, {"tags": ["python", "sql", "macos", "sqlite"], "owner": {"reputation": 337, "user_id": 11947569, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/e1658d4b682f8d91d9192916d10a5cc4?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "srb7", "link": "https://stackoverflow.com/users/11947569/srb7"}, "is_answered": false, "view_count": 325, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 58112803, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/58112803/python-script-reading-db-file-from-local-network-on-mac-via-sqlite", "title": "Python script reading db file from local network on Mac via SQlite"}, {"tags": ["python", "json", "replace", "python-requests"], "owner": {"reputation": 11, "user_id": 11860139, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/31cee1e8881a3a701f57f64739a11c9f?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "pgsteven89", "link": "https://stackoverflow.com/users/11860139/pgsteven89"}, "is_answered": true, "view_count": 57, "accepted_answer_id": 57914923, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 57897788, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/57897788/replace-function-not-working-when-ran-directly-from-python-requests-reponse", "title": "Replace function not working when ran directly from python-requests reponse"}, {"tags": ["python", "django", "database", "git", "database-connection"], "owner": {"reputation": 492, "user_id": 10223873, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/8f998151a1073137daa7891e9038a7a4?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/10223873/joelad"}, "is_answered": true, "view_count": 181, "accepted_answer_id": 57969454, "answer_count": 1, "score": -3, "last_activity_date": **********, "creation_date": **********, "question_id": 57969122, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/57969122/is-there-a-way-to-fix-a-database-isssue-on-django", "title": "is there a way to fix a database isssue on django?"}, {"tags": ["python", "amazon-web-services", "apache-spark", "cassandra", "pyspark"], "owner": {"reputation": 4756, "user_id": 2337243, "user_type": "registered", "accept_rate": 93, "profile_image": "https://www.gravatar.com/avatar/a8e587c57417b7bb83e45f5b8ab891f5?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Souad", "link": "https://stackoverflow.com/users/2337243/souad"}, "is_answered": true, "view_count": 3156, "accepted_answer_id": 46675457, "answer_count": 2, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 43741233, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/43741233/cant-connect-to-cassandra-from-pyspark", "title": "Can&#39;t connect to cassandra from Pyspark"}, {"tags": ["python", "sql", "pandas", "pyodbc", "mdf"], "owner": {"reputation": 393, "user_id": 10396945, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/-J2bLF2Qng6g/AAAAAAAAAAI/AAAAAAAAAAA/AAN31DU1ALwe-e9axc4M14dBWtNr4a-41g/mo/photo.jpg?sz=256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/10396945/matthew"}, "is_answered": true, "view_count": 10047, "accepted_answer_id": 53321484, "answer_count": 1, "score": 4, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 53307559, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/53307559/how-to-open-a-sql-server-mdf-file-with-python-pandas", "title": "How to open a SQL Server .mdf file with Python (pandas)"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 135, "user_id": 274780, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/3d1eaa1a736689326a7fee5d000ac703?s=256&d=identicon&r=PG", "display_name": "s.shpiz", "link": "https://stackoverflow.com/users/274780/s-shpiz"}, "is_answered": true, "view_count": 1202, "answer_count": 3, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 10573688, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/10573688/i-am-sporadically-getting-this-error-sqlite3-operationalerror-unable-to-open", "title": "I am sporadically getting this error : sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "keras", "path", "windows-10", "h5py"], "owner": {"reputation": 18320, "user_id": 10133797, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/tJgVr.png?s=256&g=1", "display_name": "OverLordGoldDragon", "link": "https://stackoverflow.com/users/10133797/overlordgolddragon"}, "is_answered": true, "view_count": 40, "accepted_answer_id": 57453357, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 57453302, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/57453302/oserror-unable-to-create-file-upon-adding-one-character-to-h5-save-name", "title": "&quot;OSError: Unable to create file&quot; upon adding one character to h5 save name"}, {"tags": ["python", "django", "database"], "owner": {"reputation": 1, "user_id": 11786944, "user_type": "registered", "profile_image": "https://graph.facebook.com/10219378121980098/picture?type=large", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/11786944/alae-badioui"}, "is_answered": false, "view_count": 49, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 57168023, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/57168023/operationalerror-at-result-unable-to-open-database-file", "title": "OperationalError at /result unable to open database file"}, {"tags": ["python", "python-3.x", "postgresql"], "owner": {"reputation": 589, "user_id": 11156319, "user_type": "registered", "profile_image": "https://lh4.googleusercontent.com/-k5i_FkZ6y4U/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rcHNKmqUDsgzzrwFj0vdKPMLZNCew/mo/photo.jpg?sz=256", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/11156319/linu"}, "is_answered": true, "view_count": 827, "accepted_answer_id": 57059819, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 57059445, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/57059445/reading-a-remote-text-file-using-python", "title": "Reading a remote text file using python"}, {"tags": ["python", "django", "python-3.x", "sqlite"], "owner": {"reputation": 448, "user_id": 7287584, "user_type": "registered", "accept_rate": 100, "profile_image": "https://lh6.googleusercontent.com/-4mKpxVOxC7M/AAAAAAAAAAI/AAAAAAAACns/lPoVD2PFQKk/photo.jpg?sz=256", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/7287584/gabsii"}, "is_answered": false, "view_count": 839, "answer_count": 0, "score": 6, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 56861624, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/56861624/django-unable-to-open-database-file-sqlite3-operationalerror", "title": "Django - Unable to open database file (sqlite3.OperationalError)"}, {"tags": ["python", "django", "apache", "sqlite", "mod-wsgi"], "owner": {"reputation": 118, "user_id": 4228182, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/67f3916860f01a791fbe238f91fa9f11?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "and<PERSON><PERSON>", "link": "https://stackoverflow.com/users/4228182/andrroy"}, "is_answered": false, "view_count": 1108, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 42496454, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/42496454/django-apache-mod-wsgi-unable-to-open-database-file", "title": "Django Apache mod_wsgi - Unable to open database file"}, {"tags": ["python", "django", "permissions", "gnupg", "pythonanywhere"], "owner": {"reputation": 243, "user_id": 11104072, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/-Oletek1jOEE/AAAAAAAAAAI/AAAAAAAAACU/WUDxGP83XRU/photo.jpg?sz=256", "display_name": "JcoOnline", "link": "https://stackoverflow.com/users/11104072/jcoonline"}, "is_answered": false, "view_count": 2366, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 56531719, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/56531719/permissionerror-errno-13-permission-denied-when-uploading-a-file-to-a-django", "title": "PermissionError: [<PERSON>rr<PERSON> 13] Permission denied when uploading a file to a Django-Pythonanywhere Website (GNUPG)"}, {"tags": ["python", "postgresql", "visual-studio-code", "psql"], "owner": {"reputation": 1068, "user_id": 7927748, "user_type": "registered", "accept_rate": 67, "profile_image": "https://www.gravatar.com/avatar/38dbf047126e18716a649dcd93ee9f27?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Tor<PERSON>", "link": "https://stackoverflow.com/users/7927748/torc"}, "is_answered": false, "view_count": 560, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 56401681, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/56401681/cannot-re-execute-code-until-manually-shutdown-local-postgresql-server", "title": "Cannot re-execute code until manually shutdown local PostgreSQL server"}, {"tags": ["python", "sqlite", "machine-learning", "chatbot"], "owner": {"reputation": 241, "user_id": 9545957, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/0ac3a310081fe4d26d977e4ef3ef6372?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/9545957/daniel-dos-santos"}, "is_answered": true, "view_count": 56, "closed_date": **********, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 56317506, "link": "https://stackoverflow.com/questions/56317506/my-database-isnt-connecting-sqlite3-operationalerror", "closed_reason": "Not suitable for this site", "title": "My database isn&#39;t connecting, sqlite3.OperationalError"}, {"tags": ["python", "database", "pandas", "sqlite"], "owner": {"reputation": 45, "user_id": 10502223, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/e32b199d1757404adac1980ac1b13cbc?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/10502223/mitans<PERSON>-reshamwala"}, "is_answered": true, "view_count": 33, "accepted_answer_id": 56301678, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 56301577, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/56301577/sqlite-database-is-not-connecting", "title": "sqlite database is not connecting"}, {"tags": ["python", "flask", "celery", "pytest", "celery-task"], "owner": {"reputation": 11, "user_id": 4837135, "user_type": "registered", "profile_image": "https://lh5.googleusercontent.com/-uV5Y2wgTNf0/AAAAAAAAAAI/AAAAAAAAHAs/HNAlt45ENww/photo.jpg?sz=256", "display_name": "uma parvathy", "link": "https://stackoverflow.com/users/4837135/uma-parvathy"}, "is_answered": false, "view_count": 582, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 56252578, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/56252578/how-to-mock-or-unit-test-the-celery-task-custom-update", "title": "how to mock or unit test the celery task custom update"}, {"tags": ["python", "excel", "vba", "win32com", "<PERSON><PERSON><PERSON><PERSON>"], "owner": {"reputation": 13, "user_id": 10128433, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/53bf047e06dbdc5656c2acf4bd8dc632?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "sai", "link": "https://stackoverflow.com/users/10128433/sai"}, "is_answered": false, "view_count": 226, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 52166742, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/52166742/run-excel-vba-code-from-newly-created-excel-file-using-python", "title": "Run Excel VBA code from newly created Excel file using Python"}, {"tags": ["python", "google-app-engine", "sqlite"], "owner": {"user_type": "does_not_exist", "display_name": "user809487"}, "is_answered": true, "view_count": 1708, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 6441369, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/6441369/error-while-running-google-app-engine-unable-to-open-database-file", "title": "Error while running Google App Engine : unable to open database file"}, {"tags": ["python", "sqlite", "flask-sqlalchemy"], "owner": {"reputation": 13, "user_id": 4347234, "user_type": "registered", "profile_image": "https://lh4.googleusercontent.com/-QFnZRKs1FOA/AAAAAAAAAAI/AAAAAAAAADQ/MnSpzbny2uc/photo.jpg?sz=256", "display_name": "<PERSON><PERSON> T", "link": "https://stackoverflow.com/users/4347234/moni-t"}, "is_answered": false, "view_count": 534, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 55670971, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/55670971/sqlite3-operationalerror-unable-to-open-database-file-disaster-response-pipeli", "title": "sqlite3.OperationalError: unable to open database file (Disaster Response Pipelines)"}, {"tags": ["python", "sql", "sqlite"], "owner": {"reputation": 120, "user_id": 10749624, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/VNnwI.jpg?s=256&g=1", "display_name": "Salih MSA", "link": "https://stackoverflow.com/users/10749624/salih-msa"}, "is_answered": false, "view_count": 79, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 55548328, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/55548328/executeinsert-gives-operationalerror-unable-to-open-database-file", "title": ".execute(&quot;INSERT gives OperationalError: unable to open database file"}, {"tags": ["python", "flask", "sqlite", "sqlalchemy"], "owner": {"reputation": 103, "user_id": 8804039, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/01459b3a95bc830f15db0570c9bcd7f9?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/8804039/connor-d"}, "is_answered": true, "view_count": 2757, "accepted_answer_id": 55369897, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 55368579, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/55368579/how-to-fix-error-unable-to-open-database-file-in-flask-app", "title": "How to fix &#39;Error: unable to open database file&#39; in Flask app?"}, {"tags": ["python", "sql-server", "pyodbc", "freetds", "unixodbc"], "owner": {"reputation": 389, "user_id": 3450313, "user_type": "registered", "accept_rate": 83, "profile_image": "https://www.gravatar.com/avatar/f16b9e286248f1bb7f078fe56ef36ab1?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "RJH2", "link": "https://stackoverflow.com/users/3450313/rjh2"}, "is_answered": true, "view_count": 3634, "accepted_answer_id": 38106959, "answer_count": 4, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 37933369, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/37933369/error-connecting-to-ms-sql-server-using-pyodbc-unixodbc-and-freetds-on-a-mac", "title": "Error connecting to MS SQL Server using pyODBC, unixODBC and FreeTDS (on a Mac)"}, {"tags": ["python", "django", "exception"], "owner": {"reputation": 15308, "user_id": 523507, "user_type": "registered", "accept_rate": 94, "profile_image": "https://i.stack.imgur.com/5JZJn.png?s=256&g=1", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/523507/manuelbetancurt"}, "is_answered": true, "view_count": 13086, "accepted_answer_id": 10073971, "answer_count": 3, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 10071658, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/10071658/django-unable-to-open-database-file", "title": "django unable to open database file"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 39, "user_id": 10149325, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1457cd79397fbdd0e56864c6abeed923?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Mart", "link": "https://stackoverflow.com/users/10149325/mart"}, "is_answered": false, "view_count": 913, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 54766250, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/54766250/unable-to-create-database-in-sqlite3-with-python", "title": "Unable to create database in sqlite3 with python"}, {"tags": ["python", "sqlalchemy"], "owner": {"reputation": 548, "user_id": 8554833, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/3b86aa74d3bc4949800e902d3c802747?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON> 54321", "link": "https://stackoverflow.com/users/8554833/david-54321"}, "is_answered": false, "view_count": 48, "answer_count": 1, "score": -2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 54729284, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/54729284/im-getting-started-with-sqlalchemy-im-getting-operational-errors-while-coping", "title": "I&#39;m getting started with sqlalchemy. I&#39;m getting operational errors while coping and pasting the example code"}, {"tags": ["python", "sqlalchemy", "pyodbc", "db2-400"], "owner": {"reputation": 17081, "user_id": 5405967, "user_type": "registered", "accept_rate": 67, "profile_image": "https://www.gravatar.com/avatar/b464064be975b5cd11a74fe78377c0a1?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/5405967/marredcheese"}, "is_answered": true, "view_count": 2490, "accepted_answer_id": 54242278, "answer_count": 1, "score": 4, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 54241513, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/54241513/how-do-i-access-as-400-using-sqlalchemy", "title": "How do I access AS/400 using SQLAlchemy?"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 18337, "user_id": 778942, "user_type": "registered", "accept_rate": 53, "profile_image": "https://www.gravatar.com/avatar/f98033f7f2031b092d5ff33d0f32c9da?s=256&d=identicon&r=PG", "display_name": "sam", "link": "https://stackoverflow.com/users/778942/sam"}, "is_answered": true, "view_count": 4511, "answer_count": 5, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 9583257, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/9583257/sqlite3-operationalerror-unable-to-open-database-file", "title": "sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "python-3.x", "python-2.7", "flask", "swagger"], "owner": {"reputation": 368, "user_id": 3617165, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/Gv6Q4.png?s=256&g=1", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/3617165/alejomarchan"}, "is_answered": true, "view_count": 3735, "accepted_answer_id": 53437189, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 53268702, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/53268702/python-swagger-trouble", "title": "Python Swagger Trouble"}, {"tags": ["python", "import", "flask"], "owner": {"reputation": 354, "user_id": 1623886, "user_type": "registered", "accept_rate": 50, "profile_image": "https://www.gravatar.com/avatar/d05728a0613658cb5b2f652eb6f36783?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1623886/jamie-b"}, "is_answered": true, "view_count": 18540, "accepted_answer_id": 12118422, "answer_count": 9, "score": 15, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 12118192, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/12118192/flaskr-tutorial-cant-import-flaskr-initialize-database", "title": "flaskr tutorial; can&#39;t import flaskr (initialize database)"}, {"tags": ["python", "django", "postgresql", "django-migrations"], "owner": {"reputation": 2073, "user_id": 8382028, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/fa7b207ef400cc69c5f58a89718bc155?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "ViaTech", "link": "https://stackoverflow.com/users/8382028/viatech"}, "is_answered": true, "view_count": 1213, "accepted_answer_id": 53336416, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 53328962, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/53328962/create-or-alter-postgresql-table-to-ensure-django-models-match", "title": "Create or Alter PostgreSQL Table to ensure Django Models match"}, {"tags": ["python", "django", "sqlite", "web", "raspberry-pi"], "owner": {"reputation": 436, "user_id": 5834740, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/-MaOkS56_G4M/AAAAAAAAAAI/AAAAAAAAAHk/G43yTa4xynY/photo.jpg?sz=256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/5834740/tim-b"}, "is_answered": true, "view_count": 1566, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 53213744, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/53213744/changing-location-of-sqlite-database-on-rpi-running-django", "title": "Changing location of sqlite database on rpi running django"}, {"tags": ["python", "flask", "sqlite", "sqlalchemy"], "owner": {"reputation": 753, "user_id": 4572582, "user_type": "registered", "accept_rate": 78, "profile_image": "https://graph.facebook.com/10205108582468547/picture?type=large", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/4572582/bruce-nielson"}, "is_answered": true, "view_count": 4526, "accepted_answer_id": 28546434, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 28546346, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/28546346/sqlalchemy-db-create-all-demo-fails", "title": "SQLAlchemy db.create_all() demo fails"}, {"tags": ["python", "pypi"], "owner": {"reputation": 3497, "user_id": 2562137, "user_type": "registered", "accept_rate": 77, "profile_image": "https://i.stack.imgur.com/3cxTg.png?s=256&g=1", "display_name": "Order<PERSON>nd<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/2562137/orderandchaos"}, "is_answered": false, "view_count": 223, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 52605929, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/52605929/when-trying-to-package-a-python-project-it-cant-find-sqlite3-database-after-pip", "title": "When trying to package a Python project it can&#39;t find Sqlite3 database after pip install"}, {"tags": ["python", "apache", "flask", "sqlite"], "owner": {"reputation": 49, "user_id": 7461943, "user_type": "registered", "accept_rate": 50, "profile_image": "https://www.gravatar.com/avatar/a8fe3a0f16fac296c66f62c08d78b8da?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/7461943/tirumala"}, "is_answered": false, "view_count": 425, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 52512606, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/52512606/flask-operationalerror-unable-to-open-database-file-in-memory-data-base-file", "title": "Flask: OperationalError: unable to open database file (in memory data base file)"}, {"tags": ["python", "pandas"], "owner": {"reputation": 167, "user_id": 10045428, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/bf25c715cc0e14d561641bb95b01dc51?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "juancar", "link": "https://stackoverflow.com/users/10045428/juancar"}, "is_answered": false, "view_count": 55, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 52135148, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/52135148/calculate-column-from-two-others-using-a-function-with-pandas", "title": "Calculate column from two others using a function with <PERSON>das"}, {"tags": ["python", "sqlite", "cursors"], "owner": {"reputation": 171, "user_id": 6418536, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/b84cf1f4f058af178ece771d8fe6697f?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "HaDoMin", "link": "https://stackoverflow.com/users/6418536/hadomin"}, "is_answered": false, "view_count": 52, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 52113171, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/52113171/sqlite-cursor-creates-a-new-db-with-the-same-name-as-the-one-i-want-it-to-open", "title": "SQLite cursor creates a new .db with the same name as the one I want it to open"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 15, "user_id": 10106168, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/-dqUJRx1TLKU/AAAAAAAAAAI/AAAAAAAAAAA/AAnnY7p3vN4nungmlYWhMdMm-KfK5vFurQ/mo/photo.jpg?sz=256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/10106168/alex"}, "is_answered": true, "view_count": 121, "accepted_answer_id": 51569060, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 51568990, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/51568990/i-need-a-hint-for-sqlite3", "title": "I need a hint for sqlite3"}, {"tags": ["python", "scala", "csv", "apache-spark"], "owner": {"reputation": 61, "user_id": 4123745, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/d975fc41e21020cd39fd1be2247404ae?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/4123745/jake-urban"}, "is_answered": true, "view_count": 4567, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 50751687, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/50751687/spark-incorrectly-reading-csv", "title": "Spark Incorrectly Reading CSV"}, {"tags": ["python", "django", "apache"], "owner": {"reputation": 6034, "user_id": 4738175, "user_type": "registered", "accept_rate": 56, "profile_image": "https://i.stack.imgur.com/L9De4.jpg?s=256&g=1", "display_name": "OBX", "link": "https://stackoverflow.com/users/4738175/obx"}, "is_answered": false, "view_count": 98, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 50491192, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/50491192/operational-error-unable-to-open-database-file", "title": "Operational Error : unable to open database file"}, {"tags": ["python", "python-3.x", "python-2.7", "fedora", "psycopg2"], "owner": {"reputation": 822, "user_id": 470530, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/DwuXu.jpg?s=256&g=1", "display_name": "V&#233;race", "link": "https://stackoverflow.com/users/470530/v%c3%a9race"}, "is_answered": true, "view_count": 247, "closed_date": **********, "answer_count": 1, "score": -2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 50489276, "link": "https://stackoverflow.com/questions/50489276/python-invalid-syntax-in-both-2-7-and-3-6-but-at-different-places-revised-c", "closed_reason": "Not suitable for this site", "title": "Python - invalid syntax in both 2.7 and 3.6, but at different places - revised code"}, {"tags": ["python", "django", "apache", "sqlite", "bitnami"], "owner": {"reputation": 11, "user_id": 9156216, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/5f49fae50d9142e0bfd73c780f09d292?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "vish<PERSON>h", "link": "https://stackoverflow.com/users/9156216/vishwesh"}, "is_answered": false, "view_count": 367, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 50350535, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/50350535/how-to-solve-operation-error-unable-to-open-database-file", "title": "How to solve &quot; Operation error unable to open database file&quot;?"}, {"tags": ["python", "flask", "flask-sqlalchemy"], "owner": {"reputation": 19, "user_id": 3654174, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/44ecb0266e5dab0c36ef3f6c7af75314?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "majidse", "link": "https://stackoverflow.com/users/3654174/majidse"}, "is_answered": false, "view_count": 350, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 50068613, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/50068613/flask-sqlalchemy-operationalerror-unable-to-open-database-file", "title": "flask sqlalchemy OperationalError: unable to open database file"}, {"tags": ["python", "sqlite", "csv", "sqlalchemy", "odo"], "owner": {"reputation": 23, "user_id": 9229069, "user_type": "registered", "profile_image": "https://lh3.googleusercontent.com/-v7P-Tt4rfv4/AAAAAAAAAAI/AAAAAAAAAD8/OBYNjIRxkq8/photo.jpg?sz=256", "display_name": "SaeedMMX", "link": "https://stackoverflow.com/users/9229069/saeedmmx"}, "is_answered": false, "view_count": 1605, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 49985657, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/49985657/operationalerror-sqlite3-operationalerror-unable-to-open-database-file", "title": "OperationalError: (sqlite3.OperationalError) unable to open database file"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 2620, "user_id": 1450312, "user_type": "registered", "accept_rate": 52, "profile_image": "https://www.gravatar.com/avatar/44e0ed5edde36892f9caac0bdf6f1e24?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "nad", "link": "https://stackoverflow.com/users/1450312/nad"}, "is_answered": true, "view_count": 10880, "accepted_answer_id": 49886737, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "question_id": 49886483, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/49886483/python-sqlite3-connect-unable-to-open-database-file", "title": "python sqlite3.connect - unable to open database file"}, {"tags": ["python", "django", "sqlite", "graphite"], "owner": {"reputation": 6794, "user_id": 911576, "user_type": "registered", "accept_rate": 62, "profile_image": "https://i.stack.imgur.com/2pDZR.jpg?s=256&g=1", "display_name": "anish", "link": "https://stackoverflow.com/users/911576/anish"}, "is_answered": true, "view_count": 5183, "accepted_answer_id": 26885352, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 26884974, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/26884974/operationalerror-unable-to-open-database-file", "title": "OperationalError: unable to open database file"}, {"tags": ["python", "python-3.x", "ms-access", "pyodbc", "comtypes"], "owner": {"reputation": 81, "user_id": 9503798, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/50b2cbf3dd3dc4122f6eb8ed85497e3e?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "KDM", "link": "https://stackoverflow.com/users/9503798/kdm"}, "is_answered": false, "view_count": 406, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 49324024, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/49324024/python-access-via-pyodbc-unable-to-open-registry-key-temporary-volatile-et", "title": "Python + Access via pyodbc: Unable to open registry key Temporary (volatile), etc"}, {"tags": ["python", "audio", "audio-fingerprinting"], "owner": {"reputation": 8716, "user_id": 3421652, "user_type": "registered", "accept_rate": 79, "profile_image": "https://i.stack.imgur.com/myqs7.jpg?s=256&g=1", "display_name": "Roman", "link": "https://stackoverflow.com/users/3421652/roman"}, "is_answered": true, "view_count": 2262, "accepted_answer_id": 30304786, "answer_count": 2, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 30237162, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/30237162/additional-information-for-songs-recognised-by-dejavu-py", "title": "Additional information for songs recognised by dejavu.py"}, {"tags": ["python", "django", "apache", "migrate"], "owner": {"reputation": 1, "user_id": 9231468, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/c95ba029198bf222e4502ce673a5ca79?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/9231468/jabronious"}, "is_answered": false, "view_count": 434, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 48509375, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/48509375/django-cannot-migrate", "title": "Django - Cannot &#39;migrate&#39;"}, {"tags": ["python", "database", "sqlite"], "owner": {"reputation": 65, "user_id": 8686413, "user_type": "registered", "accept_rate": 25, "profile_image": "https://www.gravatar.com/avatar/e82e88849c8afee4ec2161180712dce5?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "dg123", "link": "https://stackoverflow.com/users/8686413/dg123"}, "is_answered": false, "view_count": 70, "answer_count": 1, "score": -3, "last_activity_date": **********, "creation_date": **********, "question_id": 47611305, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/47611305/returning-a-database-with-locations", "title": "Returning a database with locations"}, {"tags": ["python", "amazon-web-services", "aws-lambda"], "owner": {"reputation": 668, "user_id": 2425811, "user_type": "registered", "accept_rate": 86, "profile_image": "https://www.gravatar.com/avatar/fcbe60b023a69aa4ff0aec37b2de29ce?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "CrizR", "link": "https://stackoverflow.com/users/2425811/crizr"}, "is_answered": true, "view_count": 969, "accepted_answer_id": 46283659, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 46282576, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/46282576/cannot-open-database-file-error-when-running-on-aws-lambda", "title": "Cannot open database file error when running on AWS lambda"}, {"tags": ["python", "django"], "owner": {"reputation": 1135, "user_id": 1788781, "user_type": "registered", "accept_rate": 88, "profile_image": "https://i.stack.imgur.com/I0sRP.jpg?s=256&g=1", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/1788781/milad-jafari"}, "is_answered": true, "view_count": 2451, "accepted_answer_id": 21060219, "answer_count": 3, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 21060172, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/21060172/unable-to-open-database-file-in-django", "title": "&quot;unable to open database file&quot; in django"}, {"tags": ["python", "apache", "flask", "sqlite", "server"], "owner": {"reputation": 63, "user_id": 8597819, "user_type": "registered", "profile_image": "https://lh6.googleusercontent.com/-UXSr8j85KJA/AAAAAAAAAAI/AAAAAAAAAG8/iu9BfTMa-SU/photo.jpg?sz=256", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/8597819/zach-dobbs"}, "is_answered": true, "view_count": 2160, "accepted_answer_id": 46227185, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 46208907, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/46208907/flask-operationalerror-unable-to-open-database-file", "title": "Flask: OperationalError: unable to open database file"}, {"tags": ["python", "decorator", "python-decorators"], "owner": {"reputation": 1871, "user_id": 4160889, "user_type": "registered", "accept_rate": 74, "profile_image": "https://www.gravatar.com/avatar/d43eac2219446a6e5a4ec0162b67c8e8?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/4160889/foreverlearner"}, "is_answered": true, "view_count": 281, "accepted_answer_id": 45942547, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "question_id": 45942491, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/45942491/python-decorated-function-unable-to-return-the-correct-value", "title": "Python decorated function unable to return the correct value"}, {"tags": ["python", "sqlite", "setuid"], "owner": {"reputation": 21, "user_id": 7228287, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/afb60a68e430b46a9bf19a7b31b08ccf?s=256&d=identicon&r=PG", "display_name": "gvolt", "link": "https://stackoverflow.com/users/7228287/gvolt"}, "is_answered": false, "view_count": 60, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 44992700, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/44992700/python-using-sqlite-after-setuid", "title": "Python: using SQLite after setuid"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 466, "user_id": 7592705, "user_type": "registered", "accept_rate": 50, "profile_image": "https://www.gravatar.com/avatar/d58eca278cf89e21707546e93b749633?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/7592705/mike"}, "is_answered": false, "view_count": 33, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 44063547, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/44063547/difficulty-opening-sql-database", "title": "Difficulty opening sql database"}, {"tags": ["python", "django", "apache", "sqlite", "permissions"], "owner": {"reputation": 5331, "user_id": 5969463, "user_type": "registered", "accept_rate": 32, "profile_image": "https://www.gravatar.com/avatar/06e2e97566b6ce069153f71673cdb455?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "MadPhysicist", "link": "https://stackoverflow.com/users/5969463/madphysicist"}, "is_answered": true, "view_count": 424, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 43596675, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/43596675/operationalerror-at-unable-to-open-database-file-appears-sporadically", "title": "OperationalError at / unable to open database file Appears Sporadically"}, {"tags": ["python", "scrapyd"], "owner": {"reputation": 21, "user_id": 7865291, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/82be833e62bc93c4c194979260ec9b48?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "vishal", "link": "https://stackoverflow.com/users/7865291/vishal"}, "is_answered": false, "view_count": 237, "answer_count": 0, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 43405384, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/43405384/scrapyd-deploy-job-fails-with-unable-to-open-database-file", "title": "scrapyd deploy job fails with unable to open database file"}, {"tags": ["python", "csv", "psycopg2"], "owner": {"reputation": 786, "user_id": 7044005, "user_type": "registered", "accept_rate": 92, "profile_image": "https://www.gravatar.com/avatar/6f8295f3213beea9f4d4fe016aa5a013?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/7044005/jake-wagner"}, "is_answered": true, "view_count": 1317, "accepted_answer_id": 43075067, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 43074474, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/43074474/write-the-headers-query-to-the-csv", "title": "Write the headers &amp; query to the csv"}, {"tags": ["python", "hive", "impyla"], "owner": {"reputation": 11, "user_id": 6129176, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/8c16c27fb6cab626aafbaa32b7fd1e51?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/6129176/alexander-vinokur"}, "is_answered": false, "view_count": 1182, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 42956778, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/42956778/cant-connect-to-hiveserver2-using-impyla", "title": "Can&#39;t connect to Hiveserver2 using impyla"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 9, "user_id": 5919249, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/447d049800506e4be1114f811325517b?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Alparslan", "link": "https://stackoverflow.com/users/5919249/alparslan"}, "is_answered": false, "view_count": 642, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 35843419, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/35843419/sqlalchemy-sqlite3-operational-error", "title": "SQLAlchemy sqlite3.operational error"}, {"tags": ["python", "sqlite", "flask"], "owner": {"reputation": 1699, "user_id": 1012040, "user_type": "registered", "accept_rate": 100, "profile_image": "https://www.gravatar.com/avatar/5929b169f798f0e7c06aee3898c907ce?s=256&d=identicon&r=PG", "display_name": "acpigeon", "link": "https://stackoverflow.com/users/1012040/acpigeon"}, "is_answered": true, "view_count": 13552, "accepted_answer_id": 12876470, "answer_count": 2, "score": 11, "last_activity_date": **********, "creation_date": **********, "question_id": 12876172, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/12876172/flask-operationalerror-unable-to-open-database-file-using-sqlite3", "title": "Flask OperationalError: unable to open database file using sqlite3"}, {"tags": ["python", "linux", "sqlite"], "owner": {"reputation": 9074, "user_id": 2295516, "user_type": "registered", "accept_rate": 86, "profile_image": "https://www.gravatar.com/avatar/d3db568933ab6e7d2a7920709236bfb7?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/2295516/don-smythe"}, "is_answered": false, "view_count": 112, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 41659802, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/41659802/get-path-in-linux-environment-variable-for-python-app", "title": "get path in linux environment variable for python app"}, {"tags": ["python", "database", "windows", "flask"], "owner": {"reputation": 1, "user_id": 7417322, "user_type": "registered", "profile_image": "https://graph.facebook.com/10208413687642335/picture?type=large", "display_name": "<PERSON><PERSON>&#235;l <PERSON><PERSON>", "link": "https://stackoverflow.com/users/7417322/micha%c3%abl-zenner"}, "is_answered": false, "view_count": 65, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 41645714, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/41645714/unable-to-open-database-file-in-flask-python-msqlite3-os-is-windows-7", "title": "Unable to open database file in flask python msqlite3. OS is windows 7"}, {"tags": ["python", "windows"], "owner": {"reputation": 501, "user_id": 7285922, "user_type": "registered", "accept_rate": 73, "profile_image": "https://www.gravatar.com/avatar/2703a601fc9d76a2f2de260db2e87b50?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Scalextrix", "link": "https://stackoverflow.com/users/7285922/scalextrix"}, "is_answered": false, "view_count": 783, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 41306581, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/41306581/keep-python-command-window-open-on-windows", "title": "Keep Python Command window open on Windows"}, {"tags": ["python", "sqlite", "docker", "docker-compose", "docker-volume"], "owner": {"reputation": 546, "user_id": 4816930, "user_type": "registered", "profile_image": "https://graph.facebook.com/979645338712480/picture?type=large", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/4816930/chaoste"}, "is_answered": true, "view_count": 8614, "accepted_answer_id": 41263454, "answer_count": 1, "score": 9, "last_activity_date": **********, "creation_date": **********, "question_id": 41221532, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/41221532/docker-volume-need-permissions-to-write-to-database", "title": "Docker volume - need permissions to write to database"}, {"tags": ["python", "sqlite", "permissions"], "owner": {"reputation": 37913, "user_id": 871910, "user_type": "registered", "accept_rate": 58, "profile_image": "https://www.gravatar.com/avatar/f073f5ca74b61813e689bbda8d12ba2b?s=256&d=identicon&r=PG", "display_name": "zmbq", "link": "https://stackoverflow.com/users/871910/zmbq"}, "is_answered": true, "view_count": 2832, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 40992820, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/40992820/opening-sqlite3-database-with-no-write-access", "title": "Opening sqlite3 database with no write access"}, {"tags": ["python", "linux", "django", "virtualenv"], "owner": {"reputation": 2708, "user_id": 299758, "user_type": "registered", "accept_rate": 38, "profile_image": "https://www.gravatar.com/avatar/717d3d46914650291170de55ee152953?s=256&d=identicon&r=PG", "display_name": "Fanooos", "link": "https://stackoverflow.com/users/299758/fanooos"}, "is_answered": true, "view_count": 1366, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "question_id": 40263870, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/40263870/python-virtualenv-can-not-access-file-in-home-directory", "title": "python virtualenv can not access file in home directory"}, {"tags": ["python", "django"], "owner": {"reputation": 4022, "user_id": 2209008, "user_type": "registered", "accept_rate": 85, "profile_image": "https://i.stack.imgur.com/MceJL.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/2209008/colin-basnett"}, "is_answered": true, "view_count": 2516, "accepted_answer_id": 40185797, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 40185366, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/40185366/perplexing-django-sqlite3-unable-to-open-database-file-error", "title": "Perplexing Django sqlite3 &quot;unable to open database file&quot; error"}, {"tags": ["python", "sql", "sql-server", "database", "sqlite"], "owner": {"reputation": 67, "user_id": 2823747, "user_type": "registered", "accept_rate": 55, "profile_image": "https://www.gravatar.com/avatar/9e71a6c974b83de1dc80b594b95ebfe3?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user2823747", "link": "https://stackoverflow.com/users/2823747/user2823747"}, "is_answered": true, "view_count": 3310, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 39970495, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/39970495/connect-to-database-on-local-host-with-sqlite3-in-python", "title": "Connect to Database on local host with sqlite3 in python"}, {"tags": ["python", "linux", "amazon-web-services", "amazon-ec2", "mysql-python"], "owner": {"reputation": 2484, "user_id": 1104955, "user_type": "registered", "accept_rate": 82, "profile_image": "https://www.gravatar.com/avatar/a53db73752628dff7f20b66644549d24?s=256&d=identicon&r=PG", "display_name": "goose", "link": "https://stackoverflow.com/users/1104955/goose"}, "is_answered": true, "view_count": 3297, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 39148410, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/39148410/importerror-no-module-named-mysql", "title": "ImportError: No module named _mysql"}, {"tags": ["python", "django", "windows", "iis-6"], "owner": {"reputation": 1, "user_id": 6725144, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/8065a9d796dc705d94f167c25695e129?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "bogun", "link": "https://stackoverflow.com/users/6725144/bogun"}, "is_answered": true, "view_count": 918, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "question_id": 38991164, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/38991164/djangooperationalerror-at-admin-login-unable-to-open-database-file", "title": "django:OperationalError at /admin/login/ unable to open database file"}, {"tags": ["python", "csv", "cx-oracle"], "owner": {"reputation": 399, "user_id": 2004245, "user_type": "registered", "accept_rate": 88, "profile_image": "https://www.gravatar.com/avatar/e920589b2e222c993eea4b533f8ec848?s=256&d=identicon&r=PG", "display_name": "user2004245", "link": "https://stackoverflow.com/users/2004245/user2004245"}, "is_answered": false, "view_count": 295, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 38462706, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/38462706/python-cx-oracle-and-csv-extracts-saved-differently-with-different-executions", "title": "Python cx_Oracle and csv extracts saved differently with different executions"}, {"tags": ["python", "firefox", "cookies", "sqlite"], "owner": {"reputation": 1100, "user_id": 2769527, "user_type": "registered", "accept_rate": 83, "profile_image": "https://i.stack.imgur.com/2F76n.jpg?s=256&g=1", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/2769527/amriteya"}, "is_answered": true, "view_count": 115, "accepted_answer_id": 38138745, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 38138533, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/38138533/sqlite-version-for-firefox-40-0", "title": "Sqlite version for Firefox 40.0"}, {"tags": ["python", "django"], "owner": {"reputation": 1, "user_id": 6468128, "user_type": "registered", "profile_image": "https://lh6.googleusercontent.com/-JAqqr_mI98s/AAAAAAAAAAI/AAAAAAAAAA4/LS_zCnXGuC8/photo.jpg?sz=256", "display_name": "<PERSON><PERSON><PERSON> balan", "link": "https://stackoverflow.com/users/6468128/khushal-balan"}, "is_answered": false, "view_count": 1452, "answer_count": 1, "score": -4, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 37828762, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/37828762/django-django-db-utils-operationalerror-unable-to-open-database-file", "title": "Django : django.db.utils.OperationalError: unable to open database file"}, {"tags": ["python", "sqlite", "kivy", "<PERSON><PERSON><PERSON>"], "owner": {"reputation": 150, "user_id": 5249200, "user_type": "registered", "accept_rate": 80, "profile_image": "https://www.gravatar.com/avatar/43bd3381a6d4784091a337cb551a6eed?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Jobiwan", "link": "https://stackoverflow.com/users/5249200/jobiwan"}, "is_answered": true, "view_count": 581, "accepted_answer_id": 37779412, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 33402893, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/33402893/buildozer-sqlite3-operational-error", "title": "Buildozer sqlite3 operational error"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 3121, "user_id": 4979733, "user_type": "registered", "accept_rate": 78, "profile_image": "https://lh3.googleusercontent.com/-x2WgsC4Euv8/AAAAAAAAAAI/AAAAAAAAAAA/JGqytYIG2kI/photo.jpg?sz=256", "display_name": "user4979733", "link": "https://stackoverflow.com/users/4979733/user4979733"}, "is_answered": true, "view_count": 5899, "accepted_answer_id": 37200445, "answer_count": 1, "score": 6, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 37199897, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/37199897/python-using-sqlite3-with-multiprocessing", "title": "Python: Using sqlite3 with multiprocessing"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 75, "user_id": 6010010, "user_type": "registered", "accept_rate": 78, "profile_image": "https://www.gravatar.com/avatar/98a10799161c79a0beeb8b75b0a2d6e6?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "michigansqllite123", "link": "https://stackoverflow.com/users/6010010/michigansqllite123"}, "is_answered": false, "view_count": 373, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 36958006, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/36958006/first-time-using-python-to-connect-with-sqlite3-getting-error", "title": "first time using python to connect with sqlite3 getting error"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 2213, "user_id": 1224963, "user_type": "registered", "accept_rate": 68, "profile_image": "https://i.stack.imgur.com/oYY72.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1224963/olive<PERSON>-kuch<PERSON><PERSON><PERSON>"}, "is_answered": true, "view_count": 3623, "accepted_answer_id": 36785023, "answer_count": 2, "score": 3, "last_activity_date": **********, "creation_date": **********, "question_id": 36784897, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/36784897/creating-database-file-one-directory-above-current", "title": "Creating database file one directory above current"}, {"tags": ["python", "sqlite", "python-2.7", "flask"], "owner": {"reputation": 929, "user_id": 1459182, "user_type": "registered", "accept_rate": 67, "profile_image": "https://i.stack.imgur.com/9aVwG.jpg?s=256&g=1", "display_name": "That_User", "link": "https://stackoverflow.com/users/1459182/that-user"}, "is_answered": true, "view_count": 8580, "accepted_answer_id": 15744674, "answer_count": 5, "score": 12, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 15740964, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/15740964/stuck-at-flask-tutorial-step-3", "title": "Stuck at Flask tutorial step 3"}, {"tags": ["python", "windows", "odbc", "subprocess"], "owner": {"reputation": 177, "user_id": 2933566, "user_type": "registered", "accept_rate": 43, "profile_image": "https://www.gravatar.com/avatar/4e48cbd531a955b30ccc8affecc16964?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Guil&#244;me", "link": "https://stackoverflow.com/users/2933566/guil%c3%b4me"}, "is_answered": false, "view_count": 1019, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 35996555, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/35996555/how-do-i-run-subprocess-command-that-involves-a-32-bit-application-from-python-6", "title": "How do I run subprocess command that involves a 32-bit application from Python 64-bit"}, {"tags": ["python", "database", "sqlite", "ubuntu", "flask-sqlalchemy"], "owner": {"reputation": 9354, "user_id": 3646408, "user_type": "registered", "accept_rate": 71, "profile_image": "https://www.gravatar.com/avatar/5816662050451a4aa29755bc882b19a9?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/3646408/abhis<PERSON><PERSON>-bhatia"}, "is_answered": true, "view_count": 4728, "accepted_answer_id": 35976606, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 35791965, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/35791965/operationalerror-unable-to-open-database-file-sqlalchemy", "title": "OperationalError: unable to open database file: sqlalchemy"}, {"tags": ["python", "python-2.7", "sqlite"], "owner": {"reputation": 1, "user_id": 6026624, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/5918380a50dd1729ea88d2c2991220c1?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user6026624", "link": "https://stackoverflow.com/users/6026624/user6026624"}, "is_answered": true, "view_count": 2281, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 35833037, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/35833037/python-sqlite3-connect-with-special-characters-in-path", "title": "Python sqlite3 connect with special characters in path"}, {"tags": ["python", "linux", "windows", "flask", "sqlite"], "owner": {"reputation": 136, "user_id": 4619760, "user_type": "registered", "accept_rate": 0, "profile_image": "https://graph.facebook.com/890867540966037/picture?type=large", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/4619760/chand-sethi"}, "is_answered": false, "view_count": 177, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 35833927, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/35833927/what-is-the-counterpart-of-piping-a-schema-into-sqlite-in-linux-to-windows-flas", "title": "What is the counterpart of piping a schema into sqlite in Linux to Windows (Flask)"}, {"tags": ["python", "function", "google-chrome", "syntax", "sqlite"], "owner": {"reputation": 25, "user_id": 4142972, "user_type": "registered", "accept_rate": 0, "profile_image": "https://www.gravatar.com/avatar/e62cef5abead8f2f3b359a38356e2ba9?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "BoBBob123", "link": "https://stackoverflow.com/users/4142972/bobbob123"}, "is_answered": false, "view_count": 332, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 34965793, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/34965793/chromesessionparser-syntax-issue", "title": "ChromeSessionParser syntax issue"}, {"tags": ["python", "flask"], "owner": {"reputation": 1, "user_id": 2875292, "user_type": "registered", "profile_image": "https://graph.facebook.com/*********/picture?type=large", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/2875292/chris-hilger"}, "is_answered": false, "view_count": 1220, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 19341481, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/19341481/flask-server-running-wrong-program", "title": "Flask Server running wrong program"}, {"tags": ["python", "flask", "sqlalchemy"], "owner": {"reputation": 2589, "user_id": 3448282, "user_type": "registered", "accept_rate": 82, "profile_image": "https://www.gravatar.com/avatar/2a98f5c9d9d2af0c95200ede47e733a4?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user3448282", "link": "https://stackoverflow.com/users/3448282/user3448282"}, "is_answered": true, "view_count": 19587, "accepted_answer_id": 29398563, "answer_count": 2, "score": 18, "last_activity_date": **********, "creation_date": **********, "question_id": 29397002, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/29397002/creating-database-with-sqlalchemy-in-flask", "title": "Creating database with SQLAlchemy in Flask"}, {"tags": ["python"], "owner": {"reputation": 1658, "user_id": 3501515, "user_type": "registered", "accept_rate": 45, "profile_image": "https://graph.facebook.com/537389158/picture?type=large", "display_name": "Codious-<PERSON>", "link": "https://stackoverflow.com/users/3501515/codious-jr"}, "is_answered": false, "view_count": 195, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 34300615, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/34300615/conceptnet5-python-setup-mac-os", "title": "Conceptnet5 python setup mac OS"}, {"tags": ["php", "python", "raspberry-pi2"], "owner": {"reputation": 74, "user_id": 4122540, "user_type": "registered", "accept_rate": 100, "profile_image": "https://www.gravatar.com/avatar/3b4eeab1f244b2050232d6b2a0c4f37a?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "jules0075", "link": "https://stackoverflow.com/users/4122540/jules0075"}, "is_answered": true, "view_count": 3102, "accepted_answer_id": 34008232, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 34008051, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/34008051/php-not-waiting-for-exec-to-finish-when-i-want-it-to", "title": "PHP not waiting for exec() to finish when I want it to"}, {"tags": ["python", "sqlite", "python-3.x", "sqlalchemy", "launchd"], "owner": {"reputation": 865, "user_id": 576838, "user_type": "registered", "accept_rate": 61, "profile_image": "https://www.gravatar.com/avatar/479d0247d03dbffca4cf87ab92665771?s=256&d=identicon&r=PG", "display_name": "user576838", "link": "https://stackoverflow.com/users/576838/user576838"}, "is_answered": true, "view_count": 387, "accepted_answer_id": 27452898, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 27390375, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/27390375/sql-alchemy-cannot-open-database-file-when-run-as-agent", "title": "SQL Alchemy Cannot Open Database File when run as agent"}, {"tags": ["python", "sqlite", "qgis"], "owner": {"reputation": 2201, "user_id": 1833602, "user_type": "registered", "accept_rate": 78, "profile_image": "https://www.gravatar.com/avatar/c3fbb2fac1915098cacb685ba8ca2013?s=256&d=identicon&r=PG", "display_name": "tobias47n9e", "link": "https://stackoverflow.com/users/1833602/tobias47n9e"}, "is_answered": false, "view_count": 210, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 33197992, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/33197992/can-one-use-or-how-to-avoid-using-a-colon-as-the-start-of-a-path-to-an-s", "title": "Can one use, or how to avoid using, a colon (&quot;:&quot;) as the start of a path to an sqlite database"}, {"tags": ["javascript", "python", "pebble-watch", "pebble-sdk", "pebble-js"], "owner": {"reputation": 179, "user_id": 4459417, "user_type": "registered", "accept_rate": 40, "profile_image": "https://www.gravatar.com/avatar/b0ee29f814ca974aac57fba0d599a635?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "hall<PERSON>", "link": "https://stackoverflow.com/users/4459417/hallole"}, "is_answered": false, "view_count": 316, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 33058491, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/33058491/cant-get-pebble-javascript-simulator-pypkjs-running", "title": "Can&#39;t get pebble javascript simulator (pypkjs) running"}, {"tags": ["python", "bittorrent", "libtorrent", "magnet-uri", "libtorrent-rasterbar"], "owner": {"user_type": "does_not_exist", "display_name": "user1642018"}, "is_answered": true, "view_count": 1609, "accepted_answer_id": 32923426, "answer_count": 1, "score": 8, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 32866890, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/32866890/creating-daemon-using-python-libtorrent-for-fetching-meta-data-of-100k-torrents", "title": "creating daemon using Python libtorrent for fetching meta data of 100k+ torrents"}, {"tags": ["python", "libtorrent", "utorrent", "magnet-uri", "libtorrent-rasterbar"], "owner": {"user_type": "does_not_exist", "display_name": "user1642018"}, "is_answered": true, "view_count": 1914, "accepted_answer_id": 32813800, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 32753998, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/32753998/why-is-utorrents-magnet-to-torrent-file-fetching-is-faster-than-my-python-script", "title": "Why is u<PERSON><PERSON><PERSON> Ma<PERSON>t to Torrent file fetching is faster than my python script?"}, {"tags": ["python", "database", "sqlite", "flask", "database-connection"], "owner": {"reputation": 59, "user_id": 667923, "user_type": "registered", "accept_rate": 100, "profile_image": "https://www.gravatar.com/avatar/65c8c5b68a2550a311b7153fdd7fdc2e?s=256&d=identicon&r=PG", "display_name": "AllofHumanity", "link": "https://stackoverflow.com/users/667923/allofhumanity"}, "is_answered": true, "view_count": 6867, "accepted_answer_id": 10874087, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 10874013, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/10874013/operationalerror-unable-to-open-database-file", "title": "OperationalError: unable to open database file"}, {"tags": ["python", "ms-access", "vba", "win32com"], "owner": {"reputation": 53, "user_id": 5350631, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/005694be379602aa231741ac266688ec?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/5350631/sirreginaldcrumbly"}, "is_answered": false, "view_count": 127, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 32715836, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/32715836/why-doesnt-my-vba-function-shut-down-access-properly", "title": "Why doesn&#39;t my VBA function shut down Access properly?"}, {"tags": ["python", "django"], "owner": {"reputation": 11, "user_id": 5252102, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/2985f4fe4b93def954d1982bf1128a90?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/5252102/liang<PERSON>oyu"}, "is_answered": false, "view_count": 104, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 32690656, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/32690656/failing-to-open-localhost-page", "title": "Failing to open localhost page"}, {"tags": ["python", "argument-passing"], "owner": {"reputation": 204047, "user_id": 434551, "user_type": "registered", "accept_rate": 81, "profile_image": "https://i.stack.imgur.com/8Wzl8.png?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/434551/r-sahu"}, "is_answered": true, "view_count": 712, "accepted_answer_id": 31735308, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 31735239, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/31735239/how-do-i-combine-known-arguments-and-variable-arguments-to-pass-them-to-another", "title": "How do I combine known arguments and variable arguments to pass them to another function?"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 5643, "user_id": 3858177, "user_type": "registered", "accept_rate": 45, "profile_image": "https://www.gravatar.com/avatar/e6b9267e3076de2cfbbde2fea7057b7c?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "gamer", "link": "https://stackoverflow.com/users/3858177/gamer"}, "is_answered": true, "view_count": 1736, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 31629489, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/31629489/django-with-sqlite-unable-to-open-database-file", "title": "django with sqlite unable to open database file"}, {"tags": ["python", "postgresql", "internal-server-error"], "owner": {"reputation": 55, "user_id": 5028323, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1a2e3766773fa4e171e830aae69060c2?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "toast9", "link": "https://stackoverflow.com/users/5028323/toast9"}, "is_answered": false, "view_count": 2376, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 30987188, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/30987188/internal-server-error-with-postgres-and-python", "title": "Internal Server Error with Postgres and Python"}, {"tags": ["python", "sqlite", "localhost", "pycharm"], "owner": {"reputation": 385, "user_id": 4426041, "user_type": "registered", "accept_rate": 78, "profile_image": "https://graph.facebook.com/**********/picture?type=large", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/4426041/finn-luca-frotscher"}, "is_answered": false, "view_count": 290, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 30349007, "content_license": "CC BY-SA 4.0", "link": "https://stackoverflow.com/questions/30349007/sqlite3-error-in-python-after-run-on-localhost-works-in-console", "title": "SQLite3 Error in Python after run on LOCALHOST; works in CONSOLE"}, {"tags": ["python", "django", "python-2.7", "django-models", "django-templates"], "owner": {"reputation": 1, "user_id": 4918246, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/c052f90e83b11917b874271cd87ef091?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON><PERSON>w", "link": "https://stackoverflow.com/users/4918246/sholi-iw"}, "is_answered": true, "view_count": 729, "answer_count": 1, "score": -3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 30337682, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/30337682/django-manage-py-syncdb-not-working", "title": "Django manage.py syncdb Not Working"}, {"tags": ["python", "mysql", "django"], "owner": {"reputation": 1003, "user_id": 3817250, "user_type": "registered", "accept_rate": 67, "profile_image": "https://i.stack.imgur.com/mSpB4.jpg?s=256&g=1", "display_name": "user3817250", "link": "https://stackoverflow.com/users/3817250/user3817250"}, "is_answered": true, "view_count": 152, "accepted_answer_id": 30041228, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 30039002, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/30039002/django-legacy-mysql-data-migration-errors", "title": "Django - Legacy MySQL data migration errors"}, {"tags": ["python", "django"], "owner": {"reputation": 8262, "user_id": 1934510, "user_type": "registered", "accept_rate": 81, "profile_image": "https://www.gravatar.com/avatar/3f2e521fe728bb5d6e87db48df3c9114?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/1934510/filipe-ferminiano"}, "is_answered": true, "view_count": 132, "accepted_answer_id": 15050949, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 15050901, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/15050901/python-2-7-with-django-sqlite-doesnt-work", "title": "Python 2.7 with Djan<PERSON>: sqlite doesn&#39;t work"}, {"tags": ["python", "django", "ubuntu", "amazon-ec2"], "owner": {"reputation": 8479, "user_id": 934703, "user_type": "registered", "accept_rate": 65, "profile_image": "https://www.gravatar.com/avatar/0379049f71460b8f1ad4cb007fff15e8?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/934703/sasha"}, "is_answered": false, "view_count": 889, "answer_count": 0, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 29457654, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/29457654/socorro-django-db-utils-operationalerror-unable-to-open-database-file", "title": "Socorro - django.db.utils.OperationalError: unable to open database file"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 1, "user_id": 2417215, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/51b8acf387d7d0241e9f8946c7cd1b69?s=256&d=identicon&r=PG", "display_name": "user2417215", "link": "https://stackoverflow.com/users/2417215/user2417215"}, "is_answered": false, "view_count": 345, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 16733517, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/16733517/creating-a-database-connection-in-python-using-sqlite", "title": "Creating a database connection in Python using SQLite"}, {"tags": ["python", "google-app-engine", "sqlite"], "owner": {"reputation": 455, "user_id": 3053085, "user_type": "registered", "accept_rate": 75, "profile_image": "https://www.gravatar.com/avatar/fcb5345be7e518147b77921ffc8b2d85?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "Levsha", "link": "https://stackoverflow.com/users/3053085/levsha"}, "is_answered": true, "view_count": 2086, "accepted_answer_id": 28348962, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 25964986, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/25964986/gae-sqlite3-operationalerror-unable-to-open-database-file", "title": "GAE, sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "django", "postgresql"], "owner": {"reputation": 61, "user_id": 3196479, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/661fbc6e772a2166a8ba13d53d0bb22d?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "user3196479", "link": "https://stackoverflow.com/users/3196479/user3196479"}, "is_answered": false, "view_count": 789, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 28267109, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/28267109/operationalerror-at-admin-login-showing-old-settings-py-values", "title": "&quot;OperationalError at /admin/login/&quot; showing old settings.py values"}, {"tags": ["python", "database", "django", "file", "file-io"], "owner": {"reputation": 5073, "user_id": 131383, "user_type": "registered", "accept_rate": 86, "profile_image": "https://www.gravatar.com/avatar/03aaca0b2d101b1e7d9b860e65197e4c?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/131383/anon"}, "is_answered": true, "view_count": 2212, "answer_count": 2, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 3293951, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/3293951/reading-files-and-writing-to-database-in-django", "title": "Reading files and writing to database in django"}, {"tags": ["python", "sqlite", "scrapy", "scrapyd"], "owner": {"reputation": 989, "user_id": 4346630, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/511cc9955c02eb14f3ba93b823467062?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/4346630/alex-napitupulu"}, "is_answered": true, "view_count": 751, "accepted_answer_id": 27408544, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 27406293, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/27406293/boot-up-scrapyd-failed-with-default-configuration-sqlite3-operationalerror-una", "title": "Boot up scrapyd failed with default configuration: sqlite3.OperationalError: unable to open database file"}, {"tags": ["python", "mysql", "unicode", "utf-8"], "owner": {"reputation": 33, "user_id": 4320616, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/de5062e846593d3d4374143b2fee0700?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "matan89", "link": "https://stackoverflow.com/users/4320616/matan89"}, "is_answered": true, "view_count": 13012, "accepted_answer_id": 27293362, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 27275839, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/27275839/how-to-fetch-and-print-utf-8-data-from-mysql-db-using-python", "title": "How to fetch and print utf-8 data from mysql DB using Python?"}, {"tags": ["python", "syncdb"], "owner": {"reputation": 21, "user_id": 2169015, "user_type": "registered", "accept_rate": 40, "profile_image": "https://www.gravatar.com/avatar/4a8773ccea85e161f93417ca8950c4e1?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/2169015/micha<PERSON>-carey"}, "is_answered": true, "view_count": 182, "accepted_answer_id": 27097528, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 27097443, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/27097443/error-when-following-the-python-anywhere-tutorial", "title": "Error when following the python anywhere tutorial"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 1, "user_id": 4205523, "user_type": "registered", "profile_image": "https://lh5.googleusercontent.com/-0_KMtk5YHqk/AAAAAAAAAAI/AAAAAAAANFc/7SjsnauwrCc/photo.jpg?sz=256", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/4205523/and<PERSON>-diaz"}, "is_answered": true, "view_count": 1759, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 26690226, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/26690226/sqlite-db-remote-file-connection", "title": "SQlite db remote file Connection"}, {"tags": ["python", "apscheduler"], "owner": {"reputation": 21, "user_id": 4176020, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/ad1ae8ced2eb6a5d037883387622ea6a?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "peepee", "link": "https://stackoverflow.com/users/4176020/peepee"}, "is_answered": true, "view_count": 6827, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 26539702, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/26539702/apscheduler-not-working-import-error-no-module-named-scheduler-found", "title": "apscheduler not working . Import Error: No module named Scheduler found"}, {"tags": ["python", "django", "postgresql", "sqlite"], "owner": {"reputation": 369, "user_id": 3897298, "user_type": "registered", "accept_rate": 74, "profile_image": "https://www.gravatar.com/avatar/0ee75a3acb5bf3349350f65dd0df4ef6?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/3897298/dan"}, "is_answered": false, "view_count": 298, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 25699383, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/25699383/why-is-django-using-sqlite3-when-i-want-to-use-postgresql", "title": "Why is django using sqlite3 when I want to use postgresql"}, {"tags": ["python", "django"], "owner": {"reputation": 1718, "user_id": 1555589, "user_type": "registered", "accept_rate": 72, "profile_image": "https://i.stack.imgur.com/nVOxT.jpg?s=256&g=1", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/1555589/mi<PERSON><PERSON><PERSON><PERSON>"}, "is_answered": true, "view_count": 513, "answer_count": 4, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 25490965, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/25490965/djangos-syncdb-and-russian-symbols-in-path", "title": "Django&#39;s Syncdb and russian symbols in path"}, {"tags": ["python", "csv", "utf-8"], "owner": {"reputation": 23, "user_id": 3980792, "user_type": "registered", "accept_rate": 0, "profile_image": "https://www.gravatar.com/avatar/931199e35bda253e92a1903f29b34469?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/3980792/hannah"}, "is_answered": false, "view_count": 1431, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 25515896, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/25515896/how-to-separate-a-utf-8-file-into-separate-rows-comma-delimited-using-python", "title": "How to separate a UTF-8 file into separate rows (comma delimited) using python?"}, {"tags": ["python", "mysql", "django"], "owner": {"reputation": 151, "user_id": 3285713, "user_type": "registered", "accept_rate": 15, "profile_image": "https://graph.facebook.com/**********/picture?type=large", "display_name": "user3285713", "link": "https://stackoverflow.com/users/3285713/user3285713"}, "is_answered": true, "view_count": 106, "accepted_answer_id": 25147572, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 25147356, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/25147356/could-not-open-database-file", "title": "Could not open database file"}, {"tags": ["python", "sqlite", "raspberry-pi", "fastcgi", "lighttpd"], "owner": {"reputation": 156, "user_id": 60455, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/254a5a40facc3d8f4dc7c6cc651577e0?s=256&d=identicon&r=PG", "display_name": "sd<PERSON><PERSON>", "link": "https://stackoverflow.com/users/60455/sdcharle"}, "is_answered": false, "view_count": 555, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 25105946, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/25105946/raspberry-pi-flask-lighttpd-operational-error-cannot-open-database-file", "title": "Raspberry Pi + Flask + lighttpd = &#39;Operational Error: cannot open database file&#39;"}, {"tags": ["python", "database", "sqlite"], "owner": {"reputation": 269, "user_id": 3622661, "user_type": "registered", "accept_rate": 0, "profile_image": "https://www.gravatar.com/avatar/1851d123a25704c41d1a5f6b5819269c?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "s<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/3622661/svetaketu"}, "is_answered": true, "view_count": 1410, "answer_count": 1, "score": 4, "last_activity_date": **********, "creation_date": **********, "question_id": 24470059, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/24470059/cant-insert-anything-into-sqlite3-database-using-python", "title": "Can&#39;t insert anything into sqlite3 Database using python"}, {"tags": ["python", "sqlite", "python-2.7", "sqlalchemy"], "owner": {"reputation": 14372, "user_id": 316082, "user_type": "registered", "accept_rate": 76, "profile_image": "https://www.gravatar.com/avatar/a18baaf9417df189af31b53af03adb1c?s=256&d=identicon&r=PG", "display_name": "liv2hak", "link": "https://stackoverflow.com/users/316082/liv2hak"}, "is_answered": true, "view_count": 2740, "accepted_answer_id": 16443860, "answer_count": 3, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 16431538, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/16431538/sqlalchemy-operationalerror-operationalerror-unable-to-open-database-file-non", "title": "sqlalchemy OperationalError: (OperationalError) unable to open database file None None"}, {"tags": ["python", "django", "apache"], "owner": {"reputation": 739, "user_id": 1614466, "user_type": "registered", "accept_rate": 48, "profile_image": "https://www.gravatar.com/avatar/2189fa29dac748038408d2c3e02221ba?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "KSHMR", "link": "https://stackoverflow.com/users/1614466/kshmr"}, "is_answered": false, "view_count": 2283, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 24293574, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/24293574/changing-file-permissions-for-apache-django", "title": "Changing file permissions for Apache - Django"}, {"tags": ["python", "mysql", "django", "apache2", "wsgi"], "owner": {"reputation": 248, "user_id": 362438, "user_type": "registered", "accept_rate": 100, "profile_image": "https://i.stack.imgur.com/dLIRu.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/362438/leonardo-pessoa"}, "is_answered": true, "view_count": 1423, "accepted_answer_id": 23632279, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 23632086, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/23632086/django-mysql-unable-to-open-database-file", "title": "Django &amp; MySQL - unable to open database file"}, {"tags": ["python"], "migrated_from": {"other_site": {"styling": {"tag_background_color": "#FFF", "tag_foreground_color": "#000", "link_color": "#0077CC"}, "related_sites": [{"relation": "meta", "api_site_parameter": "codereview.meta", "site_url": "https://codereview.meta.stackexchange.com", "name": "Code Review Meta Stack Exchange"}, {"relation": "chat", "site_url": "https://chat.stackexchange.com?tab=site&host=codereview.stackexchange.com", "name": "Chat Stack Exchange"}], "markdown_extensions": ["MathJax", "Prettify"], "launch_date": **********, "open_beta_date": **********, "closed_beta_date": **********, "site_state": "normal", "high_resolution_icon_url": "https://cdn.sstatic.net/Sites/codereview/Img/<EMAIL>", "favicon_url": "https://cdn.sstatic.net/Sites/codereview/Img/favicon.ico", "icon_url": "https://cdn.sstatic.net/Sites/codereview/Img/apple-touch-icon.png", "audience": "peer programmer code reviews", "site_url": "https://codereview.stackexchange.com", "api_site_parameter": "codereview", "logo_url": "https://cdn.sstatic.net/Sites/codereview/Img/logo.png", "name": "Code Review", "site_type": "main_site"}, "on_date": 1368547599, "question_id": 26154}, "owner": {"reputation": 1727, "user_id": 1186019, "user_type": "registered", "accept_rate": 73, "profile_image": "https://i.stack.imgur.com/kdh4h.jpg?s=256&g=1", "display_name": "root-11", "link": "https://stackoverflow.com/users/1186019/root-11"}, "is_answered": true, "view_count": 2234, "accepted_answer_id": 16568203, "answer_count": 3, "score": 1, "last_activity_date": 1391427502, "creation_date": 1368541323, "question_id": 16547881, "link": "https://stackoverflow.com/questions/16547881/python-ado-odbc-function", "title": "Python ADO + ODBC function"}, {"tags": ["python", "command-line", "sqlite"], "owner": {"reputation": 167, "user_id": 3104827, "user_type": "registered", "accept_rate": 71, "profile_image": "https://i.stack.imgur.com/54eyl.jpg?s=256&g=1", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/3104827/edyoucaterself"}, "is_answered": true, "view_count": 473, "accepted_answer_id": 21225869, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 21225482, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/21225482/python-unable-to-write-to-sqlite3-db-from-command-line", "title": "python unable to write to sqlite3 db from command line"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 343, "user_id": 743494, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/67a353fe3003a2935e4ae692f14465b0?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/743494/michaelmwu"}, "is_answered": true, "view_count": 2215, "accepted_answer_id": 21154688, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 21154180, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/21154180/frequent-operationalerror-unable-to-open-database-file-with-in-memory-sqlite3", "title": "Frequent &quot;OperationalError: unable to open database file&quot; with in memory sqlite3 database"}, {"tags": ["python", "django", "python-2.7", "django-models", "sqlite"], "owner": {"reputation": 7, "user_id": 3151641, "user_type": "registered", "accept_rate": 0, "profile_image": "https://www.gravatar.com/avatar/0764e114b9c5d2fafe8ac4e214894326?s=256&d=identicon&r=PG", "display_name": "Mat29", "link": "https://stackoverflow.com/users/3151641/mat29"}, "is_answered": false, "view_count": 186, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 20871490, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/20871490/cant-create-sqlite-database-on-windows-xp-with-django-python-2-7", "title": "Can&#39;t create sqlite database on Windows XP with Django Python 2.7"}, {"tags": ["python", "upload", "python-requests"], "owner": {"reputation": 37, "user_id": 581248, "user_type": "registered", "profile_image": "https://i.stack.imgur.com/TLzIH.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/581248/chandra"}, "is_answered": true, "view_count": 6676, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 20098761, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/20098761/python-script-for-upload-to-server", "title": "Python script for upload to server"}, {"tags": ["python", "sqlite", "ironpython", "windows-7-x64"], "owner": {"reputation": 1437, "user_id": 2043621, "user_type": "registered", "accept_rate": 88, "profile_image": "https://i.stack.imgur.com/ngBOJ.png?s=256&g=1", "display_name": "Still.<PERSON>", "link": "https://stackoverflow.com/users/2043621/still-tony"}, "is_answered": true, "view_count": 820, "accepted_answer_id": 19595786, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 19594211, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/19594211/ironpython-sqlite3-operationalerror-unable-to-open-database-file", "title": "IronPython SQLite3 OperationalError: unable to open database file"}, {"tags": ["python", "windows", "django", "sqlite", "windows-7"], "owner": {"reputation": 10646, "user_id": 67959, "user_type": "registered", "accept_rate": 85, "profile_image": "https://www.gravatar.com/avatar/73081a2c5941bf433cafc1f3e75668c7?s=256&d=identicon&r=PG", "display_name": "REA_ANDREW", "link": "https://stackoverflow.com/users/67959/rea-andrew"}, "is_answered": true, "view_count": 5526, "accepted_answer_id": 1805889, "answer_count": 6, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 1805852, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/1805852/how-can-i-use-sqlite-with-django-on-windows-7", "title": "How can I use SQLITE with DJANGO on WIndows 7"}, {"tags": ["python", "mysql", "mysql-connector", "python-3.3"], "owner": {"reputation": 1019, "user_id": 1848050, "user_type": "registered", "accept_rate": 69, "profile_image": "https://www.gravatar.com/avatar/69bbd719eda63505b2aa414fb99b7a9b?s=256&d=identicon&r=PG", "display_name": "dakov", "link": "https://stackoverflow.com/users/1848050/dakov"}, "is_answered": true, "view_count": 2120, "accepted_answer_id": 18283775, "answer_count": 1, "score": -1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 18283277, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/18283277/connector-python-select-after-insert", "title": "Connector/python select after insert"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 7932, "user_id": 915501, "user_type": "registered", "accept_rate": 60, "profile_image": "https://www.gravatar.com/avatar/b219b646efa3c74061977c39306c1841?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/915501/shep"}, "is_answered": true, "view_count": 3212, "accepted_answer_id": 18238222, "answer_count": 1, "score": 5, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 18237915, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/18237915/database-disk-image-is-malformed-from-many-concurrent-writes", "title": "Database disk image is malformed from many concurrent writes"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 2075, "user_id": 1058291, "user_type": "registered", "accept_rate": 76, "profile_image": "https://i.stack.imgur.com/D22jP.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1058291/marty"}, "is_answered": true, "view_count": 100, "accepted_answer_id": 18214111, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 18213322, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/18213322/issues-migrating-sqlite3-database-to-different-version", "title": "Issues migrating sqlite3 database to different version?"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 97, "user_id": 762450, "user_type": "registered", "accept_rate": 50, "profile_image": "https://i.stack.imgur.com/p6EaP.jpg?s=256&g=1", "display_name": "jeff", "link": "https://stackoverflow.com/users/762450/jeff"}, "is_answered": true, "view_count": 381, "accepted_answer_id": 18073038, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 18070840, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/18070840/operationerror-on-first-query-to-django-sqlite3-database", "title": "OperationError on first query to django sqlite3 database"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 15533, "user_id": 1815710, "user_type": "registered", "accept_rate": 69, "profile_image": "https://www.gravatar.com/avatar/dddf39759fed431cd1e8ecad2aa7dc49?s=256&d=identicon&r=PG", "display_name": "Liondancer", "link": "https://stackoverflow.com/users/1815710/liondancer"}, "is_answered": true, "view_count": 6746, "accepted_answer_id": 17787834, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "question_id": 17787027, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/17787027/unable-to-open-database-file-django-sqlite3", "title": "Unable to open database file Django/sqlite3"}, {"tags": ["python", "list"], "owner": {"reputation": 151, "user_id": 2583889, "user_type": "registered", "accept_rate": 100, "profile_image": "https://i.stack.imgur.com/qLrMY.jpg?s=256&g=1", "display_name": "KrisReynolds", "link": "https://stackoverflow.com/users/2583889/krisreynolds"}, "is_answered": true, "view_count": 15985, "accepted_answer_id": 17656914, "answer_count": 3, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 17656825, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/17656825/python-replacing-characters-in-a-list", "title": "Python - Replacing characters in a list"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 369, "user_id": 552285, "user_type": "registered", "accept_rate": 50, "profile_image": "https://www.gravatar.com/avatar/b3b612324a524dc6b35de9cf2f16ce24?s=256&d=identicon&r=PG", "display_name": "user552285", "link": "https://stackoverflow.com/users/552285/user552285"}, "is_answered": true, "view_count": 2425, "answer_count": 3, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 7722118, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/7722118/using-django-sqlite-in-windows", "title": "using Django Sqlite in windows"}, {"tags": ["python", "django", "sqlite", "admin"], "owner": {"reputation": 71, "user_id": 604650, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/1688a810b92b99d40179bf6f2ec88499?s=256&d=identicon&r=PG", "display_name": "kiaran_ritchie", "link": "https://stackoverflow.com/users/604650/kiaran-ritchie"}, "is_answered": false, "view_count": 178, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 16532664, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/16532664/cannot-save-manytomany-fields-from-django-admin-interface", "title": "Cannot save ManyToMany fields from Django Admin interface"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 1393, "user_id": 463676, "user_type": "registered", "accept_rate": 62, "profile_image": "https://www.gravatar.com/avatar/55172c881b22a03d57605486ee1bbe5c?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/463676/juergen-riemer"}, "is_answered": false, "view_count": 420, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 15521573, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/15521573/sqlite-python-cannot-update-record", "title": "sqlite + python: cannot update record"}, {"tags": ["python", "database", "django", "sqlite"], "owner": {"reputation": 3592, "user_id": 1124803, "user_type": "registered", "accept_rate": 38, "profile_image": "https://www.gravatar.com/avatar/59453b128d8665031e602f6f7a2b2237?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1124803/sam-mitchell"}, "is_answered": true, "view_count": 858, "answer_count": 2, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 8723947, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/8723947/unable-to-open-database-file", "title": "Unable to open database file"}, {"tags": ["python", "sqlite", "sqlalchemy"], "owner": {"reputation": 61, "user_id": 337424, "user_type": "unregistered", "profile_image": "https://www.gravatar.com/avatar/b287573a3841133787925fa6f745f854?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/337424/peter"}, "is_answered": true, "view_count": 6581, "answer_count": 3, "score": 6, "last_activity_date": **********, "creation_date": **********, "question_id": 2804106, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/2804106/operationalerror-unable-to-open-database-file-processing-query-results-with-sq", "title": "OperationalError &quot;unable to open database file&quot; processing query results with SQLAlchemy and SQLite3"}, {"tags": ["python", "mongodb"], "owner": {"reputation": 2180, "user_id": 1215031, "user_type": "registered", "accept_rate": 93, "profile_image": "https://www.gravatar.com/avatar/6ae3697b2cf910eb587338b46daf7856?s=256&d=identicon&r=PG", "display_name": "Moonwalker", "link": "https://stackoverflow.com/users/1215031/moonwalker"}, "is_answered": true, "view_count": 141, "accepted_answer_id": 13956373, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 13956054, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/13956054/can-i-change-binding-ip-for-mongod-while-mongodb-is-running", "title": "Can I change binding_ip for mongod while mongodb is running?"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 91, "user_id": 1854334, "user_type": "registered", "accept_rate": 0, "profile_image": "https://www.gravatar.com/avatar/5458e7b8640247ad773e5793f14d6142?s=256&d=identicon&r=PG", "display_name": "dexx1220", "link": "https://stackoverflow.com/users/1854334/dexx1220"}, "is_answered": true, "view_count": 320, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 13663752, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/13663752/trouble-with-sqlite-on-python-django", "title": "Trouble with Sqlite on Python/Django"}, {"tags": ["python", "sql-server", "sql-server-2008", "ssms", "<PERSON><PERSON><PERSON><PERSON>"], "owner": {"reputation": 189, "user_id": 1337783, "user_type": "registered", "accept_rate": 100, "profile_image": "https://i.stack.imgur.com/fFyUq.jpg?s=256&g=1", "display_name": "bluefoot", "link": "https://stackoverflow.com/users/1337783/bluefoot"}, "is_answered": true, "view_count": 2548, "accepted_answer_id": 13536070, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 13535822, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/13535822/set-up-and-connect-to-local-sqlexpress-database-using-sql-server-management-stud", "title": "Set up and connect to local SQLEXPRESS database using SQL Server Management Studio 2008 r2 and adodbapi"}, {"tags": ["python", "sqlite", "initialization", "database-connection", "flask"], "owner": {"reputation": 326, "user_id": 1505463, "user_type": "registered", "accept_rate": 91, "profile_image": "https://www.gravatar.com/avatar/cfb8c41548cfa9ad1d0848049b3c23fe?s=256&d=identicon&r=PG", "display_name": "matt hoover", "link": "https://stackoverflow.com/users/1505463/matt-hoover"}, "is_answered": true, "view_count": 3433, "accepted_answer_id": 13500951, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 13484771, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/13484771/operationalerror-unable-to-open-database-file-in-pycharm-with-flask-ide-plug-in", "title": "OperationalError: unable to open database file in PyCharm with Flask IDE plug-in"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 65, "user_id": 1319169, "user_type": "registered", "accept_rate": 75, "profile_image": "https://www.gravatar.com/avatar/2daa959cac153ee2ad3b00119b1fcace?s=256&d=identicon&r=PG", "display_name": "user1319169", "link": "https://stackoverflow.com/users/1319169/user1319169"}, "is_answered": false, "view_count": 497, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 10055251, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/10055251/sqlite3-operational-error-when-trying-to-configure-django", "title": "Sqlite3 operational error when trying to configure Django"}, {"tags": ["python", "bash", "serial-port"], "owner": {"reputation": 122, "user_id": 1428098, "user_type": "registered", "accept_rate": 100, "profile_image": "https://www.gravatar.com/avatar/d3a5a4dc6ba8431c0ce26eca4865256d?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/1428098/klemorali"}, "is_answered": true, "view_count": 572, "accepted_answer_id": 12017030, "answer_count": 3, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 12016029, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/12016029/moving-terminal-program-from-bash-to-python-on-linux", "title": "Moving Terminal Program from Bash to Python on Linux"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 35, "user_id": 1138218, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/903a1a37297c3f98ab1c4adfb4bc3714?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/1138218/leerix"}, "is_answered": true, "view_count": 2998, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 9256442, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/9256442/django-help-django-not-creating-sqlite-database-file-as-expected", "title": "Django--HELP. django not creating sqlite database file as expected"}, {"tags": ["python", "django", "django-models", "django-admin", "django-views"], "owner": {"reputation": 115, "user_id": 1394028, "user_type": "registered", "accept_rate": 27, "profile_image": "https://www.gravatar.com/avatar/98514f70a8b9906fdfd720602ea194bf?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1394028/joe-mo"}, "is_answered": false, "view_count": 97, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 10969945, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/10969945/generating-tables-for-installed-apps-fails", "title": "Generating tables for INSTALLED_APPS fails"}, {"tags": ["python", "file-io"], "owner": {"reputation": 2598, "user_id": 1248554, "user_type": "registered", "accept_rate": 79, "profile_image": "https://www.gravatar.com/avatar/f6bed901492ca05c57937d82512076ed?s=256&d=identicon&r=PG", "display_name": "BrtH", "link": "https://stackoverflow.com/users/1248554/brth"}, "is_answered": true, "view_count": 260, "accepted_answer_id": 12622595, "answer_count": 2, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 12622420, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/12622420/file-read-write-unexpected-behaviour", "title": "file read/write unexpected behaviour"}, {"tags": ["python", "postgresql", "psycopg2"], "owner": {"reputation": 7504, "user_id": 941397, "user_type": "registered", "accept_rate": 86, "profile_image": "https://www.gravatar.com/avatar/d85ff881af5ceab67fce4da7d62b5058?s=256&d=identicon&r=PG", "display_name": "Superdooperhero", "link": "https://stackoverflow.com/users/941397/superdooperhero"}, "is_answered": true, "view_count": 1046, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 12441583, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/12441583/python-insert-via-psycopg2-into-postgresql-failing", "title": "Python insert via psycopg2 into postgresql failing"}, {"tags": ["python", "import", "flask"], "owner": {"reputation": 69, "user_id": 1212722, "user_type": "registered", "accept_rate": 67, "profile_image": "https://www.gravatar.com/avatar/64722b2d6a4f5e7d692c2fead8be5a4e?s=256&d=identicon&r=PG", "display_name": "RetroCoNoR", "link": "https://stackoverflow.com/users/1212722/retroconor"}, "is_answered": true, "view_count": 2632, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 12289049, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/12289049/importing-init-db-flask-tutorial", "title": "Importing init_db (flask tutorial)"}, {"tags": ["python", "django"], "owner": {"reputation": 5237, "user_id": 481083, "user_type": "registered", "accept_rate": 85, "profile_image": "https://www.gravatar.com/avatar/bab76ab6bc09fcd80425e6261f1e4020?s=256&d=identicon&r=PG", "display_name": "zallarak", "link": "https://stackoverflow.com/users/481083/zallarak"}, "is_answered": true, "view_count": 5390, "accepted_answer_id": 6086273, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "question_id": 6086268, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/6086268/django-database-connection-error-sqlite3-operationalerror-unable-to-open-data", "title": "Django database connection error: &quot;sqlite3.OperationalError: unable to open database file&quot;"}, {"tags": ["c++", "python", "sqlite", "permissions", "subprocess"], "owner": {"reputation": 93, "user_id": 1248564, "user_type": "registered", "profile_image": "https://www.gravatar.com/avatar/ffeb6839115e8f6b3dae47b24aa50082?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1248564/norman-hanratty"}, "is_answered": false, "view_count": 539, "answer_count": 0, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 11221627, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/11221627/sqlite-unable-to-open-database-file-when-program-is-executed-as-subprocess", "title": "sqlite unable to open database file when program is executed as subprocess"}, {"tags": ["python", "django", "django-models", "python-3.x", "django-admin"], "owner": {"reputation": 115, "user_id": 1394028, "user_type": "registered", "accept_rate": 27, "profile_image": "https://www.gravatar.com/avatar/98514f70a8b9906fdfd720602ea194bf?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/1394028/joe-mo"}, "is_answered": true, "view_count": 1593, "accepted_answer_id": 11177929, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 11177915, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/11177915/python-and-django-fails-to-run-python-manage-py-sql-application", "title": "Python and Django fails to run [python manage.py sql application]"}, {"tags": ["python", "django", "sqlite", "<PERSON><PERSON>"], "owner": {"reputation": 3718, "user_id": 321838, "user_type": "registered", "accept_rate": 74, "profile_image": "https://www.gravatar.com/avatar/e6f67177880fb558b629820b882b159b?s=256&d=identicon&r=PG", "display_name": "t<PERSON><PERSON><PERSON>", "link": "https://stackoverflow.com/users/321838/tchaymore"}, "is_answered": true, "view_count": 2256, "answer_count": 2, "score": 1, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 8932776, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/8932776/sqlite3-cant-open-database-file", "title": "sqlite3 -- can&#39;t open database file"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 1369, "user_id": 644389, "user_type": "registered", "accept_rate": 62, "profile_image": "https://www.gravatar.com/avatar/bc15528daef59741bcb01b1677f4c3c9?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/644389/shansal"}, "is_answered": true, "view_count": 332, "accepted_answer_id": 5322333, "answer_count": 2, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 5321699, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/5321699/python-database-error", "title": "Python Database Error"}, {"tags": ["python", "database", "pyodbc"], "owner": {"reputation": 2723, "user_id": 1382299, "user_type": "registered", "accept_rate": 78, "profile_image": "https://www.gravatar.com/avatar/081393a777e07a637e70f70ef483310c?s=256&d=identicon&r=PG", "display_name": "jerry", "link": "https://stackoverflow.com/users/1382299/jerry"}, "is_answered": true, "view_count": 7510, "answer_count": 4, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 6469545, "content_license": "CC BY-SA 3.0", "link": "https://stackoverflow.com/questions/6469545/python-connecting-to-a-database-with-pyodbc-not-working", "title": "python - connecting to a database with pyodbc - not working"}, {"tags": ["python", "google-app-engine", "bulkloader"], "owner": {"reputation": 308, "user_id": 641072, "user_type": "registered", "accept_rate": 43, "profile_image": "https://www.gravatar.com/avatar/6364d6ecbc215c476f63199e402f7b9e?s=256&d=identicon&r=PG", "display_name": "abhgh", "link": "https://stackoverflow.com/users/641072/abhgh"}, "is_answered": false, "view_count": 562, "answer_count": 1, "score": 3, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 5263690, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/5263690/errors-during-downloading-data-from-google-app-engine-using-bulkloader", "title": "Errors during downloading data from Google App Engine using bulkloader"}, {"tags": ["python", "executable", "py2exe"], "owner": {"reputation": 1369, "user_id": 644389, "user_type": "registered", "accept_rate": 62, "profile_image": "https://www.gravatar.com/avatar/bc15528daef59741bcb01b1677f4c3c9?s=256&d=identicon&r=PG", "display_name": "<PERSON><PERSON>", "link": "https://stackoverflow.com/users/644389/shansal"}, "is_answered": false, "view_count": 273, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 5312284, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/5312284/python-exe-problem", "title": "Python .exe problem"}, {"tags": ["python", "database", "django"], "owner": {"reputation": 169, "user_id": 1463075, "user_type": "registered", "accept_rate": 33, "profile_image": "https://www.gravatar.com/avatar/66d19b78703437a5f0c1a4d9814478b4?s=256&d=identicon&r=PG", "display_name": "j5r", "link": "https://stackoverflow.com/users/1463075/j5r"}, "is_answered": false, "view_count": 168, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 4576940, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/4576940/django-book-question-database-config-problem-getting-operational-error", "title": "Django book question: Database config problem. Getting Operational Error"}, {"tags": ["python", "sqlite"], "owner": {"reputation": 48839, "user_id": 344286, "user_type": "registered", "accept_rate": 92, "profile_image": "https://www.gravatar.com/avatar/3827b2facd01ed6a64a96df00d4b877a?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/344286/wayne-werner"}, "is_answered": true, "view_count": 6843, "accepted_answer_id": 4190505, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 4190476, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/4190476/why-does-sqlite-tell-me-no-such-column-exists-when-i-plainly-created-it", "title": "Why does <PERSON><PERSON><PERSON> tell me no such column exists when I plainly created it?"}, {"tags": ["python", "django"], "owner": {"reputation": 1538, "user_id": 466782, "user_type": "registered", "accept_rate": 78, "profile_image": "https://www.gravatar.com/avatar/ae37a9afafe1c659829fab1cd6007719?s=256&d=identicon&r=PG", "display_name": "Prateek", "link": "https://stackoverflow.com/users/466782/prateek"}, "is_answered": true, "view_count": 149, "answer_count": 2, "score": 0, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 4030086, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/4030086/form-is-not-running-after-adding-fields-in-it", "title": "Form is not running after adding fields in it"}, {"tags": ["python", "django", "deployment", "django-database"], "owner": {"reputation": 247, "user_id": 384846, "user_type": "registered", "accept_rate": 60, "profile_image": "https://www.gravatar.com/avatar/43b645548b41c3a4582994a28a041579?s=256&d=identicon&r=PG", "display_name": "Victor", "link": "https://stackoverflow.com/users/384846/victor"}, "is_answered": true, "view_count": 186, "accepted_answer_id": 3315526, "answer_count": 1, "score": 0, "last_activity_date": **********, "creation_date": **********, "question_id": 3315314, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/3315314/problems-with-django-deployment", "title": "Problems with django deployment"}, {"tags": ["python", "django"], "owner": {"reputation": 15508, "user_id": 170005, "user_type": "registered", "accept_rate": 83, "profile_image": "https://www.gravatar.com/avatar/96ea4b5f341dc4204dac46b3f4de862b?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "fixxxer", "link": "https://stackoverflow.com/users/170005/fixxxer"}, "is_answered": true, "view_count": 1739, "accepted_answer_id": 2888549, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 2888326, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/2888326/sqlite3-operationalerror", "title": "sqlite3.OperationalError"}, {"tags": ["python", "django", "<PERSON><PERSON><PERSON>"], "owner": {"reputation": 118464, "user_id": 147601, "user_type": "registered", "accept_rate": 63, "profile_image": "https://i.stack.imgur.com/3bnPx.jpg?s=256&g=1", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/147601/nick-heiner"}, "is_answered": true, "view_count": 1264, "accepted_answer_id": 2746371, "answer_count": 1, "score": 2, "last_activity_date": **********, "creation_date": **********, "question_id": 2746342, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/2746342/pydev-and-django-pydev-breaking-django-shell", "title": "<PERSON><PERSON><PERSON><PERSON> and Django: <PERSON><PERSON><PERSON><PERSON> breaking Django shell?"}, {"tags": ["python", "django", "sqlite"], "owner": {"reputation": 7941, "user_id": 19687, "user_type": "registered", "accept_rate": 33, "profile_image": "https://www.gravatar.com/avatar/1fe2208d54af9ce3d73949de1b8b9640?s=256&d=identicon&r=PG", "display_name": "ironfroggy", "link": "https://stackoverflow.com/users/19687/ironfroggy"}, "is_answered": false, "view_count": 158, "answer_count": 1, "score": 1, "last_activity_date": **********, "creation_date": **********, "question_id": 2208643, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/2208643/django-unable-to-open-sqlite-on-some-queries-only", "title": "Django unable to open sqlite on SOME queries only?"}, {"tags": ["python", "sqlite", "windows-vista"], "owner": {"reputation": 529, "user_id": 185392, "user_type": "registered", "accept_rate": 18, "profile_image": "https://www.gravatar.com/avatar/5a2f437404ed9b44e24ba25452b7564b?s=256&d=identicon&r=PG", "display_name": "<PERSON>", "link": "https://stackoverflow.com/users/185392/dave-viner"}, "is_answered": true, "view_count": 4507, "answer_count": 2, "score": 7, "last_activity_date": **********, "creation_date": **********, "last_edit_date": **********, "question_id": 1529527, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/1529527/python-sqlite3-unable-to-open-database-file-on-windows", "title": "Python sqlite3 &quot;unable to open database file&quot; on windows"}, {"tags": ["python", "django", "ubuntu", "sqlite"], "owner": {"reputation": 959, "user_id": 209130, "user_type": "registered", "accept_rate": 94, "profile_image": "https://www.gravatar.com/avatar/b4cdd58aeda74cca2e6cbb1ac7efd163?s=256&d=identicon&r=PG", "display_name": "shawnjan", "link": "https://stackoverflow.com/users/209130/shawnjan"}, "is_answered": true, "view_count": 4107, "accepted_answer_id": 2073142, "answer_count": 1, "score": 6, "last_activity_date": **********, "creation_date": **********, "question_id": 1733050, "content_license": "CC BY-SA 2.5", "link": "https://stackoverflow.com/questions/1733050/django-error-opening-sqlite3-db-file-on-when-running-off-apache", "title": "Django error opening SQLite3 db file on when running off Apache"}]}