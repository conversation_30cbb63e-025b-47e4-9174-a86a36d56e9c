<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><defs><linearGradient id="a" x1="-264.845" y1="181.772" x2="-255.586" y2="182.061" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#bd6316"/><stop offset="1" stop-color="#4e1500"/></linearGradient><linearGradient id="b" x1="-263.276" y1="170.205" x2="-256.603" y2="171.772" xlink:href="#a"/><linearGradient id="c" x1="-265.068" y1="175.732" x2="-256.777" y2="178.801" xlink:href="#a"/><linearGradient id="d" x1="-655.014" y1="147.549" x2="-655.555" y2="146.948" gradientTransform="matrix(-0.947, -0.323, -0.323, 0.947, -558.144, -332.613)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffd436"/><stop offset="1" stop-color="#8b3f02"/></linearGradient><linearGradient id="e" x1="-258.899" y1="170.981" x2="-253.843" y2="171.325" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff8b7"/><stop offset="1" stop-color="#fcbf0e"/></linearGradient><linearGradient id="f" x1="-258.884" y1="170.975" x2="-253.896" y2="171.314" xlink:href="#a"/><linearGradient id="g" x1="-257.805" y1="172.266" x2="-257.726" y2="168.648" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" xlink:href="#d"/><linearGradient id="h" x1="-258.935" y1="181.92" x2="-256.173" y2="182.108" xlink:href="#e"/><linearGradient id="i" x1="-274.156" y1="170.544" x2="-274.512" y2="173.778" gradientTransform="matrix(-1, 0, 0, 1, -257.657, -155.509)" xlink:href="#d"/><linearGradient id="j" x1="-645.79" y1="-337.938" x2="-645.121" y2="-334.17" gradientTransform="matrix(-0.615, -0.789, -0.789, 0.615, -644.695, -286.477)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffef94"/><stop offset="1" stop-color="#ffd200"/></linearGradient><linearGradient id="k" x1="-276.128" y1="178.682" x2="-274.754" y2="178.682" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -305.235, -16.952)" xlink:href="#d"/><linearGradient id="l" x1="-274.314" y1="178.887" x2="-271.841" y2="178.987" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -305.235, -16.952)" xlink:href="#d"/><linearGradient id="m" x1="-273.817" y1="189.281" x2="-274.213" y2="174.569" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -305.235, -16.952)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#d89530"/><stop offset="1" stop-color="#772d00"/></linearGradient><linearGradient id="n" x1="-274.075" y1="174.09" x2="-273.917" y2="181.459" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -305.235, -16.952)" xlink:href="#d"/><linearGradient id="o" x1="-272.919" y1="189.281" x2="-273.315" y2="174.57" xlink:href="#m"/><linearGradient id="p" x1="-273.177" y1="174.071" x2="-273.019" y2="181.44" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -305.235, -16.952)" xlink:href="#d"/><linearGradient id="q" x1="-652.29" y1="-335.227" x2="-651.867" y2="-332.867" gradientTransform="matrix(-0.615, -0.789, -0.789, 0.615, -644.695, -286.477)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffdb57"/><stop offset="1" stop-color="#b55c13"/></linearGradient><linearGradient id="r" x1="-665.076" y1="157.413" x2="-659.751" y2="150.922" gradientTransform="matrix(-0.947, -0.323, -0.323, 0.947, -558.144, -332.613)" xlink:href="#m"/><linearGradient id="s" x1="-665.161" y1="156.114" x2="-664.822" y2="155.701" gradientTransform="matrix(-0.947, -0.323, -0.323, 0.947, -558.144, -332.613)" xlink:href="#m"/><linearGradient id="t" x1="-665.308" y1="156.436" x2="-665.836" y2="155.32" gradientTransform="matrix(-0.947, -0.323, -0.323, 0.947, -558.144, -332.613)" xlink:href="#m"/><linearGradient id="u" x1="-655.442" y1="147.568" x2="-654.817" y2="146.776" xlink:href="#d"/><linearGradient id="v" x1="-253.999" y1="175.1" x2="-254.134" y2="173.736" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" xlink:href="#m"/><linearGradient id="w" x1="-252.571" y1="173.138" x2="-252.785" y2="170.963" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" xlink:href="#m"/><linearGradient id="x" x1="-257.206" y1="169.107" x2="-259.174" y2="169.787" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" xlink:href="#m"/><linearGradient id="y" x1="-260.717" y1="170.284" x2="-260.77" y2="171.228" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" xlink:href="#m"/><linearGradient id="z" x1="-257.835" y1="174.993" x2="-257.782" y2="171.93" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" xlink:href="#d"/><linearGradient id="aa" x1="-263.928" y1="171.018" x2="-255.721" y2="171.018" gradientTransform="matrix(-0.897, 0.441, 0.441, 0.897, -292.36, -27.561)" xlink:href="#d"/><linearGradient id="ab" x1="-514.299" y1="-57.926" x2="-505.041" y2="-57.638" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#a"/><linearGradient id="ac" x1="-512.744" y1="-69.496" x2="-505.964" y2="-67.904" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#a"/><linearGradient id="ad" x1="-514.521" y1="-63.966" x2="-506.23" y2="-60.897" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#a"/><linearGradient id="ae" x1="-662.221" y1="-197.167" x2="-662.762" y2="-197.768" gradientTransform="matrix(-0.564, -0.911, -0.774, 0.455, -516.113, -497.612)" xlink:href="#d"/><linearGradient id="af" x1="-508.354" y1="-68.717" x2="-503.297" y2="-68.373" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#e"/><linearGradient id="ag" x1="-508.338" y1="-68.723" x2="-503.351" y2="-68.384" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#a"/><linearGradient id="ah" x1="-507.26" y1="-67.432" x2="-507.18" y2="-71.05" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#d"/><linearGradient id="ai" x1="-508.389" y1="-57.779" x2="-505.627" y2="-57.591" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#e"/><linearGradient id="aj" x1="-523.609" y1="-69.153" x2="-523.966" y2="-65.918" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -492.49, -86.99)" xlink:href="#d"/><linearGradient id="ak" x1="-645.333" y1="-334.169" x2="-642.973" y2="-332.091" xlink:href="#j"/><linearGradient id="al" x1="-525.581" y1="-61.015" x2="-524.207" y2="-61.015" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -492.49, -86.99)" xlink:href="#d"/><linearGradient id="am" x1="-523.768" y1="-60.81" x2="-521.294" y2="-60.71" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -492.49, -86.99)" xlink:href="#d"/><linearGradient id="an" x1="-523.27" y1="-50.416" x2="-523.666" y2="-65.127" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -492.49, -86.99)" xlink:href="#m"/><linearGradient id="ao" x1="-523.528" y1="-65.606" x2="-523.37" y2="-58.238" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -492.49, -86.99)" xlink:href="#d"/><linearGradient id="ap" x1="-522.372" y1="-50.416" x2="-522.768" y2="-65.127" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -492.49, -86.99)" xlink:href="#m"/><linearGradient id="aq" x1="-522.631" y1="-65.626" x2="-522.472" y2="-58.257" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -492.49, -86.99)" xlink:href="#d"/><linearGradient id="ar" x1="-649.116" y1="-328.591" x2="-646.827" y2="-326.795" xlink:href="#j"/><linearGradient id="as" x1="-672.284" y1="-187.303" x2="-666.958" y2="-193.795" gradientTransform="matrix(-0.564, -0.911, -0.774, 0.455, -516.113, -497.612)" xlink:href="#m"/><linearGradient id="at" x1="-672.369" y1="-188.603" x2="-672.03" y2="-189.016" gradientTransform="matrix(-0.564, -0.911, -0.774, 0.455, -516.113, -497.612)" xlink:href="#m"/><linearGradient id="au" x1="-672.516" y1="-188.281" x2="-673.044" y2="-189.397" gradientTransform="matrix(-0.564, -0.911, -0.774, 0.455, -516.113, -497.612)" xlink:href="#m"/><linearGradient id="av" x1="-662.648" y1="-197.148" x2="-662.023" y2="-197.94" gradientTransform="matrix(-0.564, -0.911, -0.774, 0.455, -516.113, -497.612)" xlink:href="#d"/><linearGradient id="aw" x1="-503.367" y1="-64.607" x2="-503.501" y2="-65.971" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#m"/><linearGradient id="ax" x1="-502.006" y1="-66.361" x2="-502.232" y2="-68.654" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#m"/><linearGradient id="ay" x1="-506.661" y1="-70.591" x2="-508.629" y2="-69.911" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#m"/><linearGradient id="az" x1="-510.172" y1="-69.414" x2="-510.224" y2="-68.47" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#m"/><linearGradient id="ba" x1="-507.289" y1="-64.705" x2="-507.236" y2="-67.768" gradientTransform="matrix(-0.947, -0.323, -0.148, 0.966, -476.554, -85.464)" xlink:href="#d"/><linearGradient id="bb" x1="-642.337" y1="-337.231" x2="-640.048" y2="-335.435" gradientTransform="matrix(-0.615, -0.789, -0.789, 0.615, -644.695, -286.477)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffdb57"/><stop offset="1" stop-color="#b66512"/></linearGradient><linearGradient id="bc" x1="-628.479" y1="-215.005" x2="-627.663" y2="-209.203" gradientTransform="matrix(-0.79, -0.614, -0.614, 0.79, -612.222, -211.392)" xlink:href="#a"/><linearGradient id="bd" x1="-633.006" y1="-211.18" x2="-622.552" y2="-211.18" gradientTransform="matrix(-0.79, -0.614, -0.614, 0.79, -612.222, -211.392)" xlink:href="#e"/><linearGradient id="be" x1="-627.491" y1="-215.556" x2="-627.755" y2="-211.648" gradientTransform="matrix(-0.79, -0.614, -0.614, 0.79, -612.222, -211.392)" xlink:href="#d"/><linearGradient id="bf" x1="-627.796" y1="-210.705" x2="-627.057" y2="-205.186" gradientTransform="matrix(-0.79, -0.614, -0.614, 0.79, -612.222, -211.392)" xlink:href="#d"/><linearGradient id="bg" x1="245.815" y1="139.434" x2="245.923" y2="136.367" gradientTransform="translate(-214.883 -165.485) rotate(7.312)" xlink:href="#e"/><linearGradient id="bh" x1="-626.269" y1="-215.112" x2="-627.643" y2="-213.078" gradientTransform="matrix(-0.79, -0.614, -0.614, 0.79, -612.222, -211.392)" xlink:href="#d"/></defs><title>file_type_license</title><polygon points="13.372 16.416 18.385 13.952 19.315 15.845 19.261 16.083 17.213 17.09 22.919 28.701 22.698 29.06 21.155 29.818 20.335 29.368 14.876 18.259 14.394 18.496 13.372 16.416" style="fill:url(#a)"/><path d="M9.24,13.619l1.2,2.44c.072.146.416.252.617.292l2.634.683L18.3,14.766l.867-2.342a.679.679,0,0,0,.036-.674l-1.2-2.44a.546.546,0,0,0-.452-.3l-4.44.136a1.278,1.278,0,0,0-1.018.541l-2.72,3.162A.916.916,0,0,0,9.24,13.619Zm1.932.033a.356.356,0,0,1,.011-.334l1.84-2.215a.829.829,0,0,1,.66-.351l2.877-.088a.355.355,0,0,1,.293.2l.356.725a.242.242,0,0,1-.139.339L11.833,14.5a.224.224,0,0,1-.3-.121Z" style="fill:url(#b)"/><path d="M9.426,13.209l1.2,2.44c.072.146.416.252.617.292l2.7.547.782,1.591L15.1,17.9l5.459,11.109.82.451L22.92,28.7l-.017-.683,0,0,.868-1.458-.241-.49-.54.1-.31-.63.41-.366L22.5,23.974l-.593-.006-.277-.564.357-.473-.3-.607-.568.043-.432-.879.382-.423-.161-.327a.871.871,0,0,1-.659-1.342l-.345-.7a1.347,1.347,0,0,1-.962-1.956l-.29-.591.631-.31-.773-1.546.843-2.277a.68.68,0,0,0,.035-.674l-1.2-2.44a.546.546,0,0,0-.453-.3L13.3,8.733a1.278,1.278,0,0,0-1.018.541l-2.84,3.419A.548.548,0,0,0,9.426,13.209Zm1.932.033a.356.356,0,0,1,.011-.334l1.84-2.215a.828.828,0,0,1,.66-.351l2.877-.088a.354.354,0,0,1,.293.2l.356.725a.242.242,0,0,1-.139.339l-5.238,2.574a.224.224,0,0,1-.3-.121Z" style="fill:url(#c)"/><polygon points="14.47 18.381 14.746 18.063 15.081 17.899 14.942 18.144 14.47 18.381" style="fill:url(#d)"/><path d="M11.259,15.828l2.776.6,4.38-2.152.865-2.339a.576.576,0,0,0,.024-.567L18.1,8.917a.441.441,0,0,0-.367-.246l-4.438.13a1.169,1.169,0,0,0-.938.5L9.52,12.725a.447.447,0,0,0-.011.414l1.206,2.453A1,1,0,0,0,11.259,15.828Zm.354-1.836,0,0-.358-.729a.463.463,0,0,1,.013-.434l0-.008L13.121,10.6a.922.922,0,0,1,.739-.389l2.881-.084a.468.468,0,0,1,.392.257l.358.729c.005.009.19.3-.187.481l-5.244,2.577C11.788,14.3,11.62,14.007,11.613,13.992Z" style="fill:url(#e)"/><path d="M11.246,15.738c.006,0,2.438.51,2.751.591l4.354-2.139c.028-.076.878-2.336.878-2.336a.472.472,0,0,0,.015-.458L18.036,8.938a.337.337,0,0,0-.282-.187L13.3,8.892a1.083,1.083,0,0,0-.864.46L9.573,12.788a.341.341,0,0,0-.008.313l1.206,2.454A1.118,1.118,0,0,0,11.246,15.738Zm.245-1.7-.355-.723a.568.568,0,0,1,.018-.535l0-.008.01-.015,1.865-2.241a1.033,1.033,0,0,1,.824-.43l2.9-.091a.575.575,0,0,1,.492.316l.359.73a.456.456,0,0,1,.027.338.484.484,0,0,1-.265.286l-5.287,2.6a.415.415,0,0,1-.331.025h0A.486.486,0,0,1,11.49,14.042Z" style="fill:url(#f)"/><path d="M11.268,15.663c.009,0,2.53.528,2.722.577l4.3-2.113.878-2.314a.419.419,0,0,0,.01-.4l-1.2-2.436a.282.282,0,0,0-.237-.156l-4.433.149a1.033,1.033,0,0,0-.82.437L10.135,12.23l-.5.595a.285.285,0,0,0,0,.254L10.828,15.5A1.181,1.181,0,0,0,11.268,15.663Zm.14-1.529-.351-.715a.575.575,0,0,1-.011-.56l1.959-2.36a1.082,1.082,0,0,1,.86-.447l2.887-.1a.628.628,0,0,1,.538.341l.356.724a.539.539,0,0,1-.309.773l-5.267,2.588A.482.482,0,0,1,11.408,14.135Z" style="fill:url(#g)"/><path d="M18.463,14.345l.7,1.433-.653.321.319.649a1.428,1.428,0,0,0,1,2.043l.3.619a.958.958,0,0,0,.706,1.436l.107.218-.39.428.5,1.011.577-.047.239.485-.366.478.336.683.6,0,.523,1.065-.419.371.382.777.549-.1.181.368C23.612,26.671,22.8,28,22.8,28v.638l-1.342.659-.729-.414L15.241,17.707l-.467.23-.7-1.433Z" style="fill:url(#h)"/><rect x="14.289" y="15.413" width="4.622" height="1.4" transform="translate(-5.405 8.973) rotate(-26.171)" style="fill:url(#i)"/><path d="M18.128,14.582l-.8.392.617,1.256.8-.392Zm-1.319.648.617,1.256.308-.152-.617-1.256Z" style="fill:url(#j)"/><path d="M18.4,16.123l.308.628a1.523,1.523,0,0,0,1.037,2.11l.263.535a1.052,1.052,0,0,0,.744,1.513l.054.111-.381.423.557,1.134.567-.043.177.36-.357.472.392.8.591.006.456.929-.409.366.45.917.539-.1.12.243L22.634,28l-1.027.5L16.082,17.265Z" style="fill:url(#k)"/><path d="M17.141,16.745l5.528,11.248.046.511-1.247.613-.613-.349L15.372,17.614Z" style="fill:url(#l)"/><polygon points="22.287 28.694 22.714 28.484 17.92 18.73 16.892 17.176 17.494 18.94 22.287 28.694" style="fill:url(#m)"/><polygon points="16.892 17.176 22.5 28.589 22.287 28.694 17.494 18.94 16.892 17.176" style="fill:url(#n)"/><polygon points="21.481 29.09 21.908 28.881 17.114 19.126 16.086 17.572 16.688 19.336 21.481 29.09" style="fill:url(#o)"/><polygon points="16.086 17.572 21.694 28.985 21.481 29.09 16.688 19.336 16.086 17.572" style="fill:url(#p)"/><path d="M18.27,16.189l-.411.2,5.285,10.755.279-.47-.181-.369-.386.072-.45-.917L22.7,25.2l-.508-1.034-.424,0-.392-.8.256-.338-.237-.482-.407.031-.557-1.134.273-.3-.143-.291a1.038,1.038,0,0,1-.619-1.259l-.391-.8a1.505,1.505,0,0,1-.906-1.844Zm-.68.334,5.371,10.929.108-.182L17.75,16.445Z" style="fill:url(#q)"/><polygon points="15.018 18.247 20.374 29.244 20.57 28.971 15.179 17.939 15.018 18.247" style="fill:url(#r)"/><polygon points="20.436 29.315 20.647 29.024 21.352 29.385 21.149 29.679 20.436 29.315" style="fill:url(#s)"/><polygon points="21.446 29.41 21.267 29.67 22.649 29.011 22.78 28.77 21.446 29.41" style="fill:url(#t)"/><polygon points="14.029 16.673 13.804 17.061 14.405 18.354 14.702 18 14.029 16.673" style="fill:url(#u)"/><polygon points="13.946 16.506 13.707 16.938 10.763 16.177 10.887 15.832 13.946 16.506" style="fill:url(#v)"/><path d="M9.359,13s-.145.444-.087.563l1.212,2.465.2-.384Z" style="fill:url(#w)"/><path d="M16.683,10.624l.1-.327-2.963.095a.914.914,0,0,0-.51.251l-.007.165a1.871,1.871,0,0,1,.394-.1Z" style="fill:url(#x)"/><path d="M16.83,10.309a.329.329,0,0,1,.17.137c.047.095.358.714.382.762l-.164.279-.341-.693c-.023-.047-.108-.145-.147-.149Z" style="fill:url(#y)"/><path d="M11.237,13.786l.171.348a.482.482,0,0,0,.661.248l5.267-2.588a.539.539,0,0,0,.309-.773l-.153-.31,1.08-.531.606,1.232a.419.419,0,0,1-.01.4l-.878,2.314-4.3,2.113c-.191-.049-2.713-.575-2.722-.577a1.181,1.181,0,0,1-.439-.16l-.6-1.222Z" style="fill:url(#z)"/><path d="M13.377,8.968l.532,1.084.539-.018L13.916,8.95Zm2.306-.077-1.395.047.533,1.084,1.4-.046Zm1.471,2.994-1.142.561,1.106,2.25,1.142-.561Zm-1.447.711-.441.217,1.106,2.251.441-.217Z" style="fill:url(#aa)"/><polygon points="10.65 14.159 15.937 15.962 15.623 18 15.45 18.134 13.29 17.397 11.369 29.898 10.998 30 9.371 29.445 8.976 28.532 10.814 16.571 10.306 16.398 10.65 14.159" style="fill:url(#ab)"/><path d="M8.951,9.175l-.542,2.747a.567.567,0,0,0,.175.452l1.976,2.461,4.866,1.659,1.969-1.077a.535.535,0,0,0,.4-.463l.4-2.627a.659.659,0,0,0-.188-.544L14.453,8.7a1.313,1.313,0,0,0-1.1-.337l-3.874.345A.707.707,0,0,0,8.951,9.175Zm1.5,1.407a.281.281,0,0,1,.192-.234l2.633-.256a.928.928,0,0,1,.724.315l2.3,2a.426.426,0,0,1,.121.352l-.1.651s-.027.237-.3.146L10.5,11.667a.269.269,0,0,1-.172-.305Z" style="fill:url(#ac)"/><path d="M9.322,9.01l-.4,2.627c-.024.157.188.481.324.653l1.817,2.33L10.8,16.334l.392.134L9.349,28.427l.395.914,1.627.555.363-.507v0l1.483-.436.081-.528-.479-.314.1-.678.523.028.2-1.283-.461-.428.093-.608.54-.087.1-.653-.469-.375.145-.946.532-.034.054-.352a1.047,1.047,0,0,1-.29-.859.688.688,0,0,1,.512-.585l.116-.756a1.6,1.6,0,0,1-.364-1.232,1.07,1.07,0,0,1,.688-.874l.1-.636.666.227.246-1.674,1.915-1.047a.535.535,0,0,0,.4-.463l.4-2.627a.657.657,0,0,0-.188-.544l-3.556-3.08a1.313,1.313,0,0,0-1.1-.336l-4.109.445A.434.434,0,0,0,9.322,9.01Zm1.5,1.407a.281.281,0,0,1,.192-.234l2.663-.289a.852.852,0,0,1,.711.218l2.3,2a.427.427,0,0,1,.122.352l-.12.781s-.027.237-.3.146L10.871,11.5A.269.269,0,0,1,10.7,11.2Z" style="fill:url(#ad)"/><polygon points="10.428 16.37 10.82 16.337 11.173 16.457 10.929 16.535 10.428 16.37" style="fill:url(#ae)"/><path d="M9.317,12.221l1.846,2.421,4.619,1.575,1.967-1.076a.454.454,0,0,0,.331-.393l.406-2.641a.53.53,0,0,0-.152-.441L14.782,8.583a1.207,1.207,0,0,0-1.011-.31l-4.109.453a.354.354,0,0,0-.237.292L9.02,11.659A1.261,1.261,0,0,0,9.317,12.221Zm1.289-1.077v0l.121-.785a.367.367,0,0,1,.25-.305l.008,0,2.675-.295a.953.953,0,0,1,.794.247l2.3,2a.563.563,0,0,1,.166.467l-.121.785c0,.01-.014.35-.411.215l-5.53-1.886A.423.423,0,0,1,10.606,11.144Z" style="fill:url(#af)"/><path d="M9.356,12.146c0,.005,1.63,2.115,1.831,2.4l4.591,1.566,1.974-1.065a.372.372,0,0,0,.264-.321l.406-2.646a.405.405,0,0,0-.118-.338L14.733,8.651a1.117,1.117,0,0,0-.931-.285L9.669,8.81a.271.271,0,0,0-.179.221l-.406,2.642A1.421,1.421,0,0,0,9.356,12.146Zm1.126-1.054.12-.778a.451.451,0,0,1,.309-.375l.008,0,.016,0,2.7-.29a1.07,1.07,0,0,1,.883.278l2.324,2.01a.694.694,0,0,1,.212.581l-.121.786a.38.38,0,0,1-.165.265.417.417,0,0,1-.365.018l-5.576-1.9a.5.5,0,0,1-.273-.219h0A.55.55,0,0,1,10.482,11.091Z" style="fill:url(#ag)"/><path d="M9.415,12.108c.006.008,1.693,2.194,1.816,2.366l4.535,1.546,1.963-1.048a.33.33,0,0,0,.228-.282l.4-2.623a.339.339,0,0,0-.1-.283L14.7,8.719a1.066,1.066,0,0,0-.884-.271l-3.4.359L9.7,8.883a.226.226,0,0,0-.142.183s-.4,2.594-.4,2.611A1.506,1.506,0,0,0,9.415,12.108Zm.952-1.007.118-.77a.463.463,0,0,1,.3-.414l2.836-.308a1.12,1.12,0,0,1,.921.292l2.316,2a.758.758,0,0,1,.234.632s-.1.633-.12.779c-.034.219-.191.5-.668.339l-5.555-1.894A.584.584,0,0,1,10.366,11.1Z" style="fill:url(#ah)"/><path d="M15.781,16.3l-.237,1.543-.689-.235-.107.7a1.158,1.158,0,0,0-.7.917,1.723,1.723,0,0,0,.367,1.283l-.1.666a.765.765,0,0,0-.527.634,1.154,1.154,0,0,0,.29.912l-.036.235-.542.031-.167,1.088.479.379-.08.522-.55.084-.113.736.471.432-.176,1.146-.532-.031-.128.837.488.317-.061.4c-.09.027-1.455.411-1.455.411l-.352.462L9.9,29.286l-.343-.822L11.4,16.434l-.493-.168.237-1.543Z" style="fill:url(#ai)"/><polygon points="11.056 16.181 15.43 17.672 15.638 16.32 11.264 14.828 11.056 16.181" style="fill:url(#aj)"/><path d="M15.388,16.234l-.842-.287L14.339,17.3l.841.287ZM14,15.76l-.208,1.353.325.111.208-1.353Z" style="fill:url(#ak)"/><path d="M14.756,17.55l-.1.676a1.251,1.251,0,0,0-.7.957A1.845,1.845,0,0,0,14.3,20.5l-.089.576a.844.844,0,0,0-.525.678,1.256,1.256,0,0,0,.275.95l-.018.119-.531.034-.188,1.221.469.375-.06.388-.539.087-.132.858.46.427-.154,1-.522-.028-.152.987.478.314-.04.262-1.506.443-1.083-.369,1.86-12.1Z" style="fill:url(#al)"/><path d="M13.423,17.1,11.563,29.2l-.246.4L10,29.16l-.289-.692,1.845-12.009Z" style="fill:url(#am)"/><polygon points="10.877 29.44 11.327 29.594 12.941 19.093 12.99 17.23 12.491 18.939 10.877 29.44" style="fill:url(#an)"/><polygon points="12.99 17.23 11.102 29.517 10.877 29.44 12.491 18.939 12.99 17.23" style="fill:url(#ao)"/><polygon points="10.028 29.151 10.477 29.304 12.091 18.803 12.14 16.94 11.641 18.65 10.028 29.151" style="fill:url(#ap)"/><polygon points="12.14 16.94 10.253 29.227 10.028 29.151 11.641 18.65 12.14 16.94" style="fill:url(#aq)"/><path d="M14.615,17.5l-.433-.148L12.4,28.932l.478-.14.061-.4L12.6,28.17l.152-.987.374.02.171-1.113-.33-.306.132-.858.387-.062.08-.519-.336-.268.188-1.221.381-.024.048-.314a1.2,1.2,0,0,1-.154-.766.85.85,0,0,1,.363-.59l.132-.856a1.79,1.79,0,0,1-.231-1.123,1.261,1.261,0,0,1,.536-.861Zm-.717-.244L12.09,29.024l.185-.054,1.791-11.655Z" style="fill:url(#ar)"/><polygon points="10.932 16.665 9.075 28.47 9.378 28.412 11.228 16.557 10.932 16.665" style="fill:url(#as)"/><polygon points="9.085 28.566 9.41 28.506 9.764 29.272 9.443 29.34 9.085 28.566" style="fill:url(#at)"/><polygon points="9.823 29.357 9.54 29.417 10.987 29.93 11.222 29.849 9.823 29.357" style="fill:url(#au)"/><polygon points="11.024 14.816 10.633 14.936 10.392 16.303 10.821 16.26 11.024 14.816" style="fill:url(#av)"/><polygon points="11.05 14.636 10.625 14.777 8.541 12.171 9.024 11.957 11.05 14.636" style="fill:url(#aw)"/><path d="M9.384,8.813s-.358.218-.378.346L8.46,11.932l.51-.253Z" style="fill:url(#ax)"/><path d="M16.435,12.332l.256-.168-2.375-2.052a.962.962,0,0,0-.538-.183l-.1.115a2.143,2.143,0,0,1,.362.211Z" style="fill:url(#ay)"/><path d="M16.724,12.208a.393.393,0,0,1,.058.221c-.016.1-.112.774-.12.825l-.283.085s.107-.7.115-.747a.4.4,0,0,0-.033-.213Z" style="fill:url(#az)"/><path d="M10.424,10.725l-.058.375a.584.584,0,0,0,.381.653L16.3,13.647c.478.163.634-.12.668-.339l.051-.334,1.139.388-.2,1.327a.33.33,0,0,1-.228.282l-1.963,1.048-4.535-1.546c-.123-.173-1.81-2.359-1.816-2.366a1.506,1.506,0,0,1-.257-.43l.2-1.315Z" style="fill:url(#ba)"/><path d="M14.756,8.764l-.179,1.166.432.373.179-1.167Zm1.851,1.595-1.119-.965-.179,1.167,1.119.965Zm-.5,3.223-1.2-.411-.372,2.423,1.2.411Zm-1.526-.52L14.12,12.9l-.372,2.423.465.159Z" style="fill:url(#bb)"/><path d="M9.936,2.733c2.042-1.441,5.206-.715,7.065,1.8A6.169,6.169,0,0,1,18.27,9.1c-.219-.73-1.023-.427-1.664-.441a4.11,4.11,0,0,0-.763-3.207C14.6,3.778,12.41,3.1,11.07,4.094s-1.382,3.253-.143,4.929a3.68,3.68,0,0,0,3.69,1.742c.521.444,1,.871,1.507,1.329-1.84,1.134-4.8.334-6.51-1.982C7.754,7.6,7.684,4.323,9.936,2.733Z" style="fill:url(#bc)"/><path d="M9.956,2.841c1.939-1.405,5.111-.606,6.892,1.8A6.587,6.587,0,0,1,18.228,8.8c-.293-.387-1.035-.264-1.466-.211a4.6,4.6,0,0,0-.823-3.272c-1.317-1.78-3.546-2.373-4.97-1.32s-1.442,3.27-.125,5.051a4.034,4.034,0,0,0,3.842,1.905c.4.35.79.715,1.185,1.073-1.982,1.012-4.528.084-6.123-2.073C7.966,7.545,7.851,4.366,9.956,2.841Z" style="fill:url(#bd)"/><path d="M10.1,2.918c1.855-1.345,4.971-.5,6.675,1.8a6.635,6.635,0,0,1,1.361,3.89,1.872,1.872,0,0,0-1.148-.126,4.555,4.555,0,0,0-.946-3.23c-1.394-1.885-3.6-2.472-5.1-1.357s-1.511,3.279-.171,5.2a4.075,4.075,0,0,0,3.976,1.973c.323.285.639.58.957.871-1.939.959-4.394-.1-5.893-2.131C8.1,7.511,8.1,4.363,10.1,2.918Z" style="fill:url(#be)"/><path d="M9.684,3.412C8.6,4.969,8.825,7.5,10.3,9.494c1.413,1.911,3.466,2.885,5.249,2.443-1.694.674-4.074-.233-5.554-2.235C8.455,7.621,8.335,4.919,9.684,3.412Z" style="fill:url(#bf)"/><path d="M9.9,3.511c-.171.291.366.627.547.144a4.29,4.29,0,0,1,2.442-1.222A3.558,3.558,0,0,0,9.9,3.511Zm.077.506a.244.244,0,1,0,.211.273A.243.243,0,0,0,9.975,4.017Z" style="fill:url(#bg)"/><path d="M11.035,3.763c1.4-1.181,3.664-.647,5.163,1.132a4.723,4.723,0,0,1,1.2,3.542c-.136.014-.273.03-.411.048a4.555,4.555,0,0,0-.946-3.23c-1.394-1.885-3.6-2.472-5.1-1.357-.071.053-.136.11-.2.167A2.672,2.672,0,0,1,11.035,3.763Z" style="fill:url(#bh)"/></svg>