<svg version="1.1" viewBox="100 100 800 800" xmlns="http://www.w3.org/2000/svg">
<style>.st0{fill:url(#XMLID_4_);} .st1{fill:url(#XMLID_5_);} .st2{fill:url(#XMLID_8_);} .st3{fill:url(#XMLID_9_);} .st4{fill:url(#XMLID_11_);} .st5{opacity:0.3;fill:url(#XMLID_16_);}</style>
<linearGradient id="XMLID_8_" x1="429.39" x2="469.39" y1="517.16" y2="559.16" gradientTransform="matrix(1.1129,0,0,1.1129,-54.117,-62.353)" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6533" offset="0"/>
<stop stop-color="#FF5633" offset=".157"/>
<stop stop-color="#FF4333" offset=".434"/>
<stop stop-color="#FF3733" offset=".714"/>
<stop stop-color="#F33" offset="1"/>
</linearGradient>
<linearGradient id="XMLID_11_" x1="450.12" x2="506.94" y1="514.21" y2="552.85" gradientTransform="matrix(1.1129,0,0,1.1129,-54.117,-62.353)" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBB040" offset="0"/>
<stop stop-color="#FB8840" offset="1"/>
</linearGradient>
<linearGradient id="XMLID_16_" x1="508.33" x2="450.33" y1="295.76" y2="933.76" gradientTransform="matrix(1.1129,0,0,1.1129,-54.117,-62.353)" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF" offset="0"/>
<stop stop-color="#FFF" stop-opacity="0" offset="1"/>
</linearGradient>
<g transform="translate(-5.272 -2.3783)">
<g id="XMLID_14_" transform="matrix(1.1129,0,0,1.1129,-54.117,-62.353)">
<linearGradient id="XMLID_4_" x1="444.47" x2="598.47" y1="526.05" y2="562.05" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6533" offset="0"/>
<stop stop-color="#FF5633" offset=".157"/>
<stop stop-color="#FF4333" offset=".434"/>
<stop stop-color="#FF3733" offset=".714"/>
<stop stop-color="#F33" offset="1"/>
</linearGradient>
<path id="XMLID_15_" class="st0" d="m721 410c0-33.6-8.8-65.1-24.3-92.4-41.1-42.3-130.5-52.1-152.7-.2-22.8 53.2 38.3 112.4 65 107.7 34-6-6-84-6-84 52 98 40 68-54 158s-190 279.9-204 287.9c-.6.4-1.2.7-1.9 1h368.7c6.5 0 10.7-6.9 7.8-12.7l-96.4-190.8c-2.1-4.1-.6-9.2 3.4-11.5 56.4-32.4 94.4-93.2 94.4-163z" fill="url(#XMLID_4_)"/>
</g>
<g id="XMLID_2_" transform="matrix(1.1129,0,0,1.1129,-54.117,-62.353)">
<linearGradient id="XMLID_5_" x1="420.38" x2="696.38" y1="475" y2="689" gradientUnits="userSpaceOnUse">
<stop stop-color="#BF3338" offset="0"/>
<stop stop-color="#F33" offset="1"/>
</linearGradient>
<path id="XMLID_10_" class="st1" d="m721 410c0-33.6-8.8-65.1-24.3-92.4-41.1-42.3-130.5-52.1-152.7-.2-22.8 53.2 38.3 112.4 65 107.7 34-6-6-84-6-84 52 98 40 68-54 158s-190 279.9-204 287.9c-.6.4-1.2.7-1.9 1h368.7c6.5 0 10.7-6.9 7.8-12.7l-96.4-190.8c-2.1-4.1-.6-9.2 3.4-11.5 56.4-32.4 94.4-93.2 94.4-163z" fill="url(#XMLID_5_)"/>
</g>
<path id="XMLID_3_" class="st2" d="m329.82 813.46c15.58-8.9028 122.41-220.34 227.02-320.5s117.96-66.771 60.094-175.83c0 0-221.46 310.49-301.58 464.06" fill="url(#XMLID_8_)" stroke-width="1.1129"/>
<g id="XMLID_7_" transform="matrix(1.1129,0,0,1.1129,-54.117,-62.353)">
<linearGradient id="XMLID_9_" x1="502.11" x2="490.11" y1="589.46" y2="417.46" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6533" offset="0"/>
<stop stop-color="#FF5633" offset=".157"/>
<stop stop-color="#FF4333" offset=".434"/>
<stop stop-color="#FF3733" offset=".714"/>
<stop stop-color="#F33" offset="1"/>
</linearGradient>
<path id="XMLID_12_" class="st3" d="m373 537c134.4-247.1 152-272 222-272 36.8 0 73.9 16.6 97.9 46.1-32.7-52.7-90.6-88-156.9-89h-228.3c-4.8 0-8.7 3.9-8.7 8.7v460.2c13.6-35.1 36.7-85.3 74-154z" fill="url(#XMLID_9_)"/>
</g>
<path id="XMLID_6_" class="st4" d="m556.84 492.96c-104.61 100.16-211.44 311.6-227.02 320.5s-41.732 10.016-55.643-5.5643c-14.801-16.582-37.837-43.401 86.802-272.65 149.57-274.99 169.15-302.7 247.05-302.7 40.953 0 82.24 18.473 108.95 51.302 1.4467 2.337 2.8934 4.7853 4.3401 7.2335-45.738-47.074-145.23-57.98-169.93-.22257-25.373 59.204 42.622 125.08 72.335 119.85 37.837-6.6771-6.6771-93.48-6.6771-93.48 57.757 108.95 44.403 75.563-60.205 175.72z" fill="url(#XMLID_11_)" stroke-width="1.1129"/>
<path id="XMLID_13_" class="st5" d="m373.22 547.49c149.57-274.99 169.15-302.7 247.05-302.7 33.719 0 67.661 12.575 93.48 35.277-26.708-30.492-66.326-47.519-105.72-47.519-77.9 0-97.486 27.71-247.05 302.7-124.64 229.25-101.6 256.07-86.802 272.65 2.1144 2.337 4.5627 4.3401 7.1222 6.0094-13.02-18.918-18.807-62.876 91.922-266.42z" fill="url(#XMLID_16_)" opacity=".3" stroke-width="1.1129"/>
</g>
</svg>
